package com.czur.starry.device.otalib

import androidx.lifecycle.MutableLiveData
import com.czur.starry.device.baselib.handler.SPContentHandler


object OTAHandler : SPContentHandler() {
    private const val KEY_VERSION_STATUS = "firmware_is_need_update"  // 版本升级

    private const val KEY_VERSION_CAMERA_VERSION = "cameraFirmwareNeedUpdate"  // 4kCamera版本升级
    private const val KEY_VERSION_CAMERA_CURRENT = "currentCameraFirmwareNeedUpdate"  // 4kCamera当前版本
    private const val KEY_VERSION_CAMERA_UPGRADE_SUCCESS = "cameraUpgradeSuccess"  // 4kCamera升级成功
    private const val KEY_VERSION_CAMERA_VERSION_CHECK = "cameraVersionCheck"  // 4kCamera主动检测
    private const val KEY_SYSTEM_TRACK_MODE_CHECK = "systemTrackModeState"  // 语音更新摄像头/麦克风设置

    private const val KEY_VERSION_CLICK_DROP_CURRENT_VERSION = "clickDropCurrentVersion"  // 无线投屏器

    private const val KEY_VERSION_FORCE_STATUS = "firmware_is_need_force_update"  // 版本强制升级
    private const val KEY_NEW_TOUCH_PAD_VERSION = "touchPadHasNewVersion"   // 触控板是否需要升级
    private const val CYCLE_REMIND_TIME = "cycle_to_remind_time"  // 提醒时间
    private const val CYCLE_REMIND_MEMORY = "cycle_to_remind_memory"  // 提醒内存
    private const val FLAG_DIFFRENT_PERIOD = "flag_diffrent_period"  // 定时周期
    private const val FLAG_LAST_DETECTION_TIME = "flag_last_detection_time"  // 上次检测时间
    private const val VERSION_UNINSTALL_FEATURE = "uninstall_app_feature"  // 卸载app特征版本
    private const val QRCODE_UPLOAD = "qrcode_upload_image"  // 扫码上传图片
    private const val LAST_QRCODE_URL = "qrcode_last_url"  // 扫码图片url
    private const val IMAGE_RECEIVED_STATE = "image_received_state"  // 扫码上传状态
    private const val RTM_CONNECTED_STATE = "rtm_connected_state"  // RTM连接状态
    const val KEY_ALL_MEETING_STATUES = "allMeetingStatus"  // 所有视频会议和本地会议录像


    override val authority: String = "com.czur.starry.device.update.otaprovider"
    override val keyLiveMap: Map<String, LiveTrans<*>> by lazy {
        mapOf(
            KEY_VERSION_STATUS to LiveTrans(newVersionStatusLive as MutableLiveData) {
                it.isNotEmpty() && it != "false"
            },
            KEY_VERSION_CAMERA_VERSION to LiveTrans(newCameraVersionStatusLive as MutableLiveData) {
                it
            },
            KEY_VERSION_CAMERA_VERSION_CHECK to LiveTrans(cameraVersionCheckLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_VERSION_CAMERA_UPGRADE_SUCCESS to LiveTrans(cameraUpgradeSuccessLive as MutableLiveData) {
                it.toBoolean()
            },
            CYCLE_REMIND_TIME to LiveTrans(cycleRemindTimeLive as MutableLiveData) {
                it.toInt()
            },
            QRCODE_UPLOAD to LiveTrans(newImageStatusLive as MutableLiveData) {
                it
            },
            IMAGE_RECEIVED_STATE to LiveTrans(imageReceivedLive as MutableLiveData) {
                it.isNotEmpty() && it != "false"
            },
            KEY_NEW_TOUCH_PAD_VERSION to LiveTrans(newTouchPadVersionLive as MutableLiveData) {
                it.toBoolean()
            },
            RTM_CONNECTED_STATE to LiveTrans(rtmStatusLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_ALL_MEETING_STATUES to LiveTrans(allMeetingStateLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_SYSTEM_TRACK_MODE_CHECK to LiveTrans(systemTrackModeStatusLive as MutableLiveData) {
                it.toBoolean()
            }

        )
    }


    /**
     * 是否有视频会议或本地会议录像
     */
    val allMeetingStateLive = createLive { allMeetingStatus }

    var allMeetingStatus: Boolean
        get() = getValue(KEY_ALL_MEETING_STATUES, false)!!
        set(value) = setValue(KEY_ALL_MEETING_STATUES, value)

    /**
     * 是否有新版本4k摄像头
     */
    val newCameraVersionStatusLive = createLive { newCameraVersion }

    var newCameraVersion: String
        get() = getValue(KEY_VERSION_CAMERA_VERSION, "null")!!
        set(value) = setValue(KEY_VERSION_CAMERA_VERSION, value)

    /**
     * 主动刷新cameraVersion 检测
     */
    val cameraVersionCheckLive = createLive { cameraVersionCheck }

    var cameraVersionCheck: Boolean
        get() = getValue(KEY_VERSION_CAMERA_VERSION_CHECK, false)!!
        set(value) = setValue(KEY_VERSION_CAMERA_VERSION_CHECK, value)

    var currentCameraVersion: String
        get() = getValue(KEY_VERSION_CAMERA_CURRENT, "400")!!
        set(value) = setValue(KEY_VERSION_CAMERA_CURRENT, value)
    /**
     * 无线投屏器version检测
     */
    var currentClickDropVersion: String
        get() = getValue(KEY_VERSION_CLICK_DROP_CURRENT_VERSION, "1")!!
        set(value) = setValue(KEY_VERSION_CLICK_DROP_CURRENT_VERSION, value)
    /**
     * 升级是否成功
     */
    val cameraUpgradeSuccessLive = createLive { cameraUpgradeSuccess }

    var cameraUpgradeSuccess: Boolean
        get() = getValue(KEY_VERSION_CAMERA_UPGRADE_SUCCESS, true)!!
        set(value) = setValue(KEY_VERSION_CAMERA_UPGRADE_SUCCESS, value)

    /**
     * 是否有新版本
     */
    val newVersionStatusLive = createLive { newVersionStatus }

    var newVersionStatus: Boolean
        get() = getValue(KEY_VERSION_STATUS, false)!!
        set(value) = setValue(KEY_VERSION_STATUS, value)

    /**
     * 是否强制升级
     */
    var forceVersionStatus: Boolean
        get() = getValue(KEY_VERSION_FORCE_STATUS, false)!!
        set(value) = setValue(KEY_VERSION_FORCE_STATUS, value)

    /**
     * 是否有新的触控板版本
     */
    val newTouchPadVersionLive = createLive { newTouchPadVersion }
    var newTouchPadVersion: Boolean
        get() = getValue(KEY_NEW_TOUCH_PAD_VERSION, false)
        set(value) = setValue(KEY_NEW_TOUCH_PAD_VERSION, value)


    /**
     * 提醒时间（检测周期）
     */
    val cycleRemindTimeLive = createLive { cycleRemindTime }

    //7 :7天; 3:3天; 1:每天;0:不提醒
    var cycleRemindTime: Int
        get() = getValue(CYCLE_REMIND_TIME, 3)!!
        set(value) = setValue(CYCLE_REMIND_TIME, value)


    /**
     * 提醒内存（检测内存大小）
     */

    //2 :2G; 1.5:1.5G; 1:1G
    var cycleRemindMemory: Float
        get() = getValue(CYCLE_REMIND_MEMORY, 2f)!!
        set(value) = setValue(CYCLE_REMIND_MEMORY, value)


    //标记是否和上次任务相同
    var isDiffrentFlag: Int
        get() = getValue(FLAG_DIFFRENT_PERIOD, 0)!!
        set(value) = setValue(FLAG_DIFFRENT_PERIOD, value)

    //标记上次检测时间
    var lastDetectionTime: String
        get() = getValue(FLAG_LAST_DETECTION_TIME, "20130101")!!
        set(value) = setValue(FLAG_LAST_DETECTION_TIME, value)

    //卸载特征表版本
    var uninstall_info: String
        get() = getValue(VERSION_UNINSTALL_FEATURE, "1.0")!!
        set(value) = setValue(VERSION_UNINSTALL_FEATURE, value)


    /**
     * 上传图片到欢迎模式
     */
    val newImageStatusLive = createLive { newImageStatus }

    var newImageStatus: String
        get() = getValue(QRCODE_UPLOAD, "")!!
        set(value) = setValue(QRCODE_UPLOAD, value)

    /**
     * 开始接受上传图片
     */
    val imageReceivedLive = createLive { imageReceivedStatus }

    var imageReceivedStatus: Boolean
        get() = getValue(IMAGE_RECEIVED_STATE, false)!!
        set(value) = setValue(IMAGE_RECEIVED_STATE, value)

    //标记上次扫码url
    var lastQcodeUrl: String
        get() = getValue(LAST_QRCODE_URL, "")!!
        set(value) = setValue(LAST_QRCODE_URL, value)

    /**
     * RTM是否连接
     */
    val rtmStatusLive = createLive { rtmConnectedStatus }
    var rtmConnectedStatus: Boolean
        get() = getValue(RTM_CONNECTED_STATE, false)!!
        set(value) = setValue(RTM_CONNECTED_STATE, value)

    /**
     *  语音更新设置UI同步刷新；降噪/增强UI ；会议摄像头;隔空手势控制UI
     */
    val systemTrackModeStatusLive = createLive { systemTrackModeStatus }

    var systemTrackModeStatus: Boolean
        get() = getValue(KEY_SYSTEM_TRACK_MODE_CHECK, false)!!
        set(value) = setValue(KEY_SYSTEM_TRACK_MODE_CHECK, value)



}