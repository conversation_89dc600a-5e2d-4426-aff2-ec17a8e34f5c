<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--  画面主体颜色  -->
    <color name="bg_main">#FFF</color>
    <color name="bg_main_blue">#5879FC</color>
    <!--  左侧菜单的背景颜色  -->
    <color name="bg_main_menu">#5879FC</color>
    <color name="bg_main_menu_cursor">#38FFFFFF</color>

    <color name="btn_bg_back">#DADFFD</color>


    <!--  左侧菜单的文字颜色  -->
    <color name="text_main_menu">#FFF</color>
    <!--  通用文字颜色  -->
    <color name="text_common">#393939</color>
    <!--  通用文字text_common70%颜色  -->
    <color name="text_common_light">#B3393939</color>
    <!--  通用警示颜色  -->
    <color name="text_common_warning">#EF4B4C</color>
    <!--  白色的文字  -->
    <color name="text_white">#FFF</color>
    <color name="text_white_hover">#dde3fe</color>
    <!--  光标颜色  -->
    <color name="text_cursor_common">#FFF</color>
    <!--  标题栏文字颜色  -->
    <color name="text_title_bar_content">#FFF</color>

    <!--  内容标题颜色  -->
    <color name="text_content_title">#393939</color>
    <color name="text_content_title_hover">#616161</color>

    <color name="text_setting_item_sel">#FFF</color>
    <color name="text_setting_item_normal">@color/text_common</color>

    <!--  按钮颜色  -->
    <!--    1. 蓝底白字-->
    <color name="cz_btn_blue_white_bg">#5879FC</color>
    <color name="cz_btn_blue_white_text">#FFF</color>
    <!--    2. 兰白底蓝字-->
    <color name="cz_btn_pale_blue_white_bg">#DADFFD</color>
    <color name="cz_btn_pale_blue_white_text">#5879FC</color>
    <!--  3. 半透白色白字  -->
    <color name="cz_btn_alpha_white_white_bg">#33FFFFFF</color>
    <color name="cz_btn_alpha_white_white_text">#FFF</color>

    <!--  4. 蓝色背景下的按钮  -->
    <color name="cz_btn_positive_bg_in_blue">#FFFFFF</color>
    <color name="cz_btn_positive_text_in_blue">#5879FC</color>
    <color name="cz_btn_negative_bg_in_blue">#33FFFFFF</color>
    <color name="cz_btn_negative_text_in_blue">#FFF</color>

    <!--  5. 浅蓝背景白字  -->
    <color name="cz_btn_little_blue_bg">#4BB6F4</color>
    <color name="cz_btn_white_text">#FFF</color>


    <!--  Switch颜色  -->
    <color name="cz_switch_bg_on">#5879FC</color>
    <color name="cz_switch_bg_off">#F1F3FE</color>
    <color name="cz_switch_thumb_on">#FFF</color>
    <color name="cz_switch_thumb_off">#5879FC</color>
    <color name="cz_switch_text_on">#FFF</color>
    <color name="cz_switch_text_off">#5879FC</color>
    <color name="cz_switch_border">#5879FC</color>

    <!--  CheckBox  -->
    <color name="cz_checkbox_bg">#5879FC</color>
    <color name="cz_checkbox_check_mark">#FFF</color>

    <!--  ProgressBar  -->
    <color name="cz_progress_bar_bg">#DADFFD</color>
    <color name="cz_progress_bar_progress">#5879FC</color>

    <color name="cz_progress_bar_bg_white">#4DDADFFD</color>
    <color name="cz_progress_bar_progress_white">#FFFFFF</color>

    <!--  通用的EditText  -->
    <color name="cz_et_text">#393939</color>
    <color name="cz_et_hint">#5879FC</color>
    <color name="cz_et_bg">#4D5879FC</color>

    <color name="cz_item_bg_normal">#F1F3FE</color>
    <color name="cz_item_bg_selected">#5879FC</color>
    <color name="cz_item_bg_hover">#DADFFD</color>
    <color name="cz_item_text_sel">#FFF</color>
    <color name="cz_text_alpha50_white">#50FFFFFF</color>
    <color name="color_26FFFFFF">#26FFFFFF</color>

</resources>