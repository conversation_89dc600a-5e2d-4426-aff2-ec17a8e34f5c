<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    tools:ignore="PxUsage">

    <ImageView
        android:id="@+id/seekDownIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_cz_seek_bar_down" />

    <Space
        android:id="@+id/spaceLeft"
        android:layout_width="30px"
        android:layout_height="wrap_content" />

    <com.czur.uilib.seek.CZSeekBarCore
        android:id="@+id/seekBarCore"
        android:layout_width="0px"
        android:layout_weight="1"
        android:layout_height="wrap_content"/>

    <Space
        android:id="@+id/spaceRight"
        android:layout_width="30px"
        android:layout_height="wrap_content" />


    <ImageView
        android:id="@+id/seekUpIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_cz_seek_bar_up"/>

</LinearLayout>