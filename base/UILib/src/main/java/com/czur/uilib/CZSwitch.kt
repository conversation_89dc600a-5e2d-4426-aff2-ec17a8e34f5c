package com.czur.uilib

import android.animation.Animator
import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import kotlin.math.min
import kotlin.math.pow

/**
 * Created by 陈丰尧 on 2023/8/1
 * Switch开关
 */
private const val THUMB_PADDING = 6F // 滑块与背景的间距
private const val TEXT_SIZE = 16F    // 文字大小
private const val TEXT_ON = "ON"     // ON文字
private const val TEXT_OFF = "OFF"   // OFF文字
private var textPadding = 15F // 文字与边框的距离
private const val BORDER_WIDTH = 2F  // 边框宽度
private const val ANIM_DURATION = 300L   // 动画时长

private const val ON_VALUE = 1F
private const val OFF_VALUE = 0F

@SuppressLint("UseKtx")
class CZSwitch @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private var animProcess = 0F    // 动画进度(0:OFF, 1:ON)
    private val isAnim      // 是否正在动画中
        get() = animProcess != 0F && animProcess != 1F

    private val paint: Paint by lazy {
        Paint().apply {
            isAntiAlias = true
        }
    }
    private val argbEvaluator = ArgbEvaluator() // 颜色估值器

    private val defColorBgOn: Int by color(R.color.cz_switch_bg_on)
    private var colorBgOn: Int
    private val defColorBgOff: Int by color(R.color.cz_switch_bg_off)
    private var colorBgOff: Int

    private val defColorThumbOn: Int by color(R.color.cz_switch_thumb_on)
    private var colorThumbOn: Int
    private val defColorThumbOff: Int by color(R.color.cz_switch_thumb_off)
    private var colorThumbOff: Int

    private val defColorTextOn: Int by color(R.color.cz_switch_text_on)
    private var colorTextOn: Int
    private val defColorTextOff: Int by color(R.color.cz_switch_text_off)
    private var colorTextOff: Int

    private val defColorBorder: Int by color(R.color.cz_switch_border)
    private var colorBorder: Int

    //置灰模式
    private var isGrayMode = false
        set(value) {
            field = value
            invalidate()
        }

    private val textBounds = Rect() // 文字边界
    private var currentAnim: ValueAnimator = ValueAnimator.ofFloat().apply {
        duration = ANIM_DURATION
        interpolator = AccelerateDecelerateInterpolator()
        addUpdateListener {
            if (it.isRunning) {
                animProcess = it.animatedValue as Float
                invalidate()
            }
        }
    }

    private val path = Path()

    private var onSwitchChangeListener: ((isOn: Boolean, fromUser: Boolean) -> Unit)? = null

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.CZSwitch)
        colorBgOn = ta.getColor(R.styleable.CZSwitch_czSwitchBgOnColor, defColorBgOn)
        colorBgOff = ta.getColor(R.styleable.CZSwitch_czSwitchBgOffColor, defColorBgOff)

        colorThumbOn = ta.getColor(R.styleable.CZSwitch_czSwitchThumbOnColor, defColorThumbOn)
        colorThumbOff = ta.getColor(R.styleable.CZSwitch_czSwitchThumbOffColor, defColorThumbOff)

        colorTextOn = ta.getColor(R.styleable.CZSwitch_czSwitchTextOnColor, defColorTextOn)
        colorTextOff = ta.getColor(R.styleable.CZSwitch_czSwitchTextOffColor, defColorTextOff)

        colorBorder = ta.getColor(R.styleable.CZSwitch_czSwitchBorderColor, defColorBorder)
        ta.recycle()

        setOnDebounceClickListener {
            if (isAnim) return@setOnDebounceClickListener   // 动画中不响应点击
            if (animProcess == OFF_VALUE) {
                // OFF -> ON
                startAnim(OFF_VALUE, ON_VALUE, true)
            } else {
                // ON -> OFF
                startAnim(ON_VALUE, OFF_VALUE, true)
            }
        }
    }

    fun setOnSwitchChangeListener(listener: ((isOn: Boolean, fromUser: Boolean) -> Unit)?) {
        onSwitchChangeListener = listener
    }

    private fun startAnim(start: Float, end: Float, fromUser: Boolean) {
        cancelAnim()
        currentAnim.setFloatValues(start, end)
        val listener = object : Animator.AnimatorListener {
            override fun onAnimationEnd(animator: Animator) {
                onSwitchChangeListener?.invoke(animProcess == ON_VALUE, fromUser)
                currentAnim.removeListener(this)
            }

            override fun onAnimationCancel(animator: Animator) {
                currentAnim.removeListener(this)
            }

            override fun onAnimationRepeat(animation: Animator) {}

            override fun onAnimationStart(animator: Animator) {}
        }
        currentAnim.addListener(listener)
        currentAnim.start()
    }


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        textPadding = if (width < 90) {
            10F
        } else {
            15F
        }
        val originalAlpha = paint.alpha
        val originalColor = paint.color

        val radio = min(width, height) / 2F

        paint.style = Paint.Style.FILL

//        paint.alpha = 255
        // 1. 绘制背景
        val bgColor = argbEvaluator.evaluate(animProcess, colorBgOff, colorBgOn) as Int
        paint.color = bgColor
        canvas.drawRoundRect(1F, 1F, width.toFloat() - 1, height.toFloat() - 1, radio, radio, paint)
        // 2. 绘制滑块
        val thumbRadio = radio - THUMB_PADDING
        val thumbX =
            (width - thumbRadio * 2 - THUMB_PADDING * 2) * animProcess + thumbRadio + THUMB_PADDING
        val thumbColor = argbEvaluator.evaluate(animProcess, colorThumbOff, colorThumbOn) as Int
        paint.color = thumbColor
        // 动画过程中, 滑块会先变大再变小
        val thumbRadioOffset = -16 * (animProcess - 0.5f).pow(2) + 4
        canvas.drawCircle(thumbX, height / 2F, thumbRadio + thumbRadioOffset, paint)
        // 3. 绘制文字
        paint.textSize = TEXT_SIZE
        paint.textAlign = Paint.Align.LEFT
        // 3.1 绘制ON文字
        paint.color = colorTextOn
        paint.alpha = (255 * animProcess).toInt()
        // 计算文字的基线 垂直居中
        val baseLineY = (height - paint.fontMetrics.top - paint.fontMetrics.bottom) / 2F
        canvas.drawText(TEXT_ON, textPadding, baseLineY, paint)
        // 3.2 绘制OFF文字
        paint.color = colorTextOff
        paint.alpha = (255 * (1 - animProcess)).toInt()
        paint.getTextBounds(TEXT_OFF, 0, TEXT_OFF.length, textBounds)

        canvas.drawText(TEXT_OFF, width - textPadding - textBounds.width(), baseLineY, paint)
        // 4. 绘制整体边框
        paint.alpha = 255
        paint.style = Paint.Style.STROKE
        paint.color = colorBorder
        paint.strokeWidth = BORDER_WIDTH
        canvas.drawRoundRect(
            BORDER_WIDTH / 2,
            BORDER_WIDTH / 2,
            width - BORDER_WIDTH / 2,
            height - BORDER_WIDTH / 2,
            radio,
            radio,
            paint
        )

        // 绘制白色遮罩，透明度 70%
        if (isGrayMode) {
            // 增强抗锯齿效果
            paint.isAntiAlias = true
            paint.isDither = true
            paint.isFilterBitmap = true

            paint.style = Paint.Style.FILL
            paint.color = 0xB3FFFFFF.toInt() // 白色 70% 透明度

            // 计算扩展后的绘制区域
            val halfBorderWidth = BORDER_WIDTH / 2
            val left = -halfBorderWidth
            val top = -halfBorderWidth
            val right = width.toFloat() + halfBorderWidth
            val bottom = height.toFloat() + halfBorderWidth

            // 使用 Path 绘制圆角矩形
            path.reset()
            path.addRoundRect(
                left,
                top,
                right,
                bottom,
                radio + halfBorderWidth, // 圆角半径也相应增加
                radio + halfBorderWidth,
                Path.Direction.CW
            )
            canvas.drawPath(path, paint)
        }
        // 恢复原始颜色和透明度
        paint.color = originalColor
        paint.alpha = originalAlpha
    }

    /**
     * 设置开关状态, 该方法的fromUser参数为false
     * @param switchOn 是否开启
     * @param useAnim 是否使用动画
     */
    fun setSwitchOn(switchOn: Boolean, useAnim: Boolean = true) {
        val targetProgress = if (switchOn) ON_VALUE else OFF_VALUE
        if (useAnim) {
            startAnim(animProcess, targetProgress, false)
        } else {
            cancelAnim()
            animProcess = targetProgress
            onSwitchChangeListener?.invoke(animProcess == ON_VALUE, false)
            invalidate()
        }
    }

    private fun cancelAnim() {
        if (currentAnim.isRunning) {
            currentAnim.cancel()
        }
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        isGrayMode = !enabled
    }

}