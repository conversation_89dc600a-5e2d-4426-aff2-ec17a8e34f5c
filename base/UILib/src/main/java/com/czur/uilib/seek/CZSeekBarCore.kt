package com.czur.uilib.seek

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import kotlin.math.roundToInt

/**
 * Created by 陈丰尧 on 2023/7/31
 * SeekBar的核心部分
 * 1. 轨道
 * 2. 滑块
 * 都是绘制的, 这个颜色是需要和按钮什么的保持一致的, 所以直接固定在代码中
 */

private const val COLOR_TRACK_PROGRESS = 0xFF5879FC.toInt()
private const val COLOR_TRACK_BACKGROUND = 0xFFDADFFD.toInt()
private const val COLOR_THUMB = 0xFF5879FC.toInt()
private const val COLOR_THUMB_OUT_LINE = 0xFFF5F6F7.toInt() // 滑块外边框颜色

private const val HEIGHT_TRACK = 6F
private const val HEIGHT_THUMB = 30F
private const val WIDTH_THUMB = 20F
private const val ROUND_THUMB = 6F
private const val WIDTH_THUMB_OUT_LINE = 3F

internal class CZSeekBarCore @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : View(context, attrs, defStyleAttr) {
    private val paint: Paint by lazy {
        Paint().apply {
            isAntiAlias = true
        }
    }

    var progress = 0
        set(value) {
            field = value
            when (value) {
                min -> {
                    onProgressReachesBoundaryListener?.invoke(Boundary.MIN)
                }
                max -> {
                    onProgressReachesBoundaryListener?.invoke(Boundary.MAX)
                }
                else -> {
                    onProgressReachesBoundaryListener?.invoke(Boundary.MID)
                }
            }
        }
    var min = 0
        set(value) {
            field = value
            onScopeChange()
        }
    var max = 100
        set(value) {
            field = value
            onScopeChange()
        }
    private val drawProgress: Float
        get() = (progress - min).toFloat() / (max - min)
    private val drawPaddingHorizontal = WIDTH_THUMB / 2F
    private val drawWidth
        get() = width - drawPaddingHorizontal * 2F

    private var isTouching = false

    var onProgressChangerListener: ProgressChangedListener? = null
    var onProgressChangeCompletedListener: ProgressChangeCompletedListener? = null
    var onProgressReachesBoundaryListener: ProgressReachesBoundaryListener? = null

    var blockUserOperation: Boolean = false


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        paint.style = Paint.Style.STROKE

        // 1. 绘制轨道
        paint.color = COLOR_TRACK_BACKGROUND
        paint.strokeWidth = HEIGHT_TRACK
        paint.strokeCap = Paint.Cap.ROUND
        canvas.drawLine(
            drawPaddingHorizontal,
            height / 2F,
            drawWidth + drawPaddingHorizontal,
            height / 2F,
            paint
        )

        // 2. 绘制进度
        paint.color = COLOR_TRACK_PROGRESS
        paint.strokeWidth = HEIGHT_TRACK
        paint.strokeCap = Paint.Cap.ROUND
        canvas.drawLine(
            drawPaddingHorizontal,
            height / 2F,
            drawWidth * drawProgress + drawPaddingHorizontal,
            height / 2F,
            paint
        )

        // 3. 绘制滑块外边框
        paint.color = COLOR_THUMB_OUT_LINE
        paint.style = Paint.Style.FILL_AND_STROKE
        canvas.drawRoundRect(
            drawWidth * drawProgress + drawPaddingHorizontal - WIDTH_THUMB / 2F,
            height / 2F - HEIGHT_THUMB / 2F,
            drawWidth * drawProgress + drawPaddingHorizontal + WIDTH_THUMB / 2F,
            height / 2F + HEIGHT_THUMB / 2F,
            ROUND_THUMB,
            ROUND_THUMB,
            paint
        )
        // 4. 绘制滑块
        paint.color = COLOR_THUMB
        paint.style = Paint.Style.FILL_AND_STROKE
        canvas.drawRoundRect(
            drawWidth * drawProgress + drawPaddingHorizontal - WIDTH_THUMB / 2F + WIDTH_THUMB_OUT_LINE,
            height / 2F - HEIGHT_THUMB / 2F + WIDTH_THUMB_OUT_LINE,
            drawWidth * drawProgress + drawPaddingHorizontal + WIDTH_THUMB / 2F - WIDTH_THUMB_OUT_LINE,
            height / 2F + HEIGHT_THUMB / 2F - WIDTH_THUMB_OUT_LINE,
            ROUND_THUMB - 2,
            ROUND_THUMB - 2 ,
            paint
        )
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (event == null || blockUserOperation) return super.onTouchEvent(event)
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                isTouching = true
                updateProgress(
                    ((event.x - drawPaddingHorizontal) / drawWidth * (max - min) + min).roundToInt(),
                    true
                )
            }

            MotionEvent.ACTION_MOVE -> {
                updateProgress(
                    ((event.x - drawPaddingHorizontal) / drawWidth * (max - min) + min).roundToInt(),
                    true
                )
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                onProgressChangeCompletedListener?.invoke(progress, true)
                isTouching = false
            }
        }
        return true
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // 高度是滑块高度
        val height = (HEIGHT_THUMB + WIDTH_THUMB_OUT_LINE).toInt()
        super.onMeasure(widthMeasureSpec, MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY))
    }

    /**
     * 当范围改变
     */
    private fun onScopeChange() {
        if (progress < min) {
            progress = min
        } else if (progress > max) {
            progress = max
        }
        invalidate()
    }

    fun updateProgress(newProgress: Int, fromUser: Boolean) {
        if (progress == newProgress) return
        progress = if (newProgress < min) {
            min
        } else if (newProgress > max) {
            max
        } else {
            newProgress
        }
        onProgressChangerListener?.invoke(progress, fromUser)
        invalidate()
    }
}