package com.czur.uilib.seek

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.uilib.R
import com.czur.uilib.UI_DISABLE_ALPHA
import com.czur.uilib.databinding.WidgetCzSeekBarBinding

/**
 * Created by 陈丰尧 on 2023/7/31
 */
private const val DEF_PROGRESS_MIN = 0
private const val DEF_PROGRESS_MAX = 100
private const val DEF_PROGRESS = 0
private const val DEF_SHOW_ACTION_BTN = true

class CZSeekBar @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {
     val binding by lazy {
        WidgetCzSeekBarBinding.inflate(LayoutInflater.from(context), this, true)
    }

    /**
     * 进度改变的回调
     */
    var onProgressChangerListener: ProgressChangedListener?
        set(value) {
            binding.seekBarCore.onProgressChangerListener = value
        }
        get() = binding.seekBarCore.onProgressChangerListener

    /**
     * 进度改变完成的回调
     * 1. 滑块拖动完成
     * 2. 点击左右两边的按钮
     */
    var onProgressChangeCompletedListener: ProgressChangedListener?
        set(value) {
            binding.seekBarCore.onProgressChangeCompletedListener = value
        }
        get() = binding.seekBarCore.onProgressChangeCompletedListener


    var min: Int
        set(value) {
            binding.seekBarCore.min = value
        }
        get() = binding.seekBarCore.min
    var max: Int
        set(value) {
            binding.seekBarCore.max = value
        }
        get() = binding.seekBarCore.max

    var progress: Int
        set(value) = binding.seekBarCore.updateProgress(value, false)
        get() = binding.seekBarCore.progress

    var blockUserOperation = false
        set(value) {
            field = value
            binding.seekBarCore.blockUserOperation = value
        }

    init {
        // 初始化SeekBar的属性
        val ta = context.obtainStyledAttributes(attrs, R.styleable.CZSeekBar)
        val showActionBtn = ta.getBoolean(R.styleable.CZSeekBar_showActionBtn, DEF_SHOW_ACTION_BTN)
        val min = ta.getInt(R.styleable.CZSeekBar_min, DEF_PROGRESS_MIN)
        val max = ta.getInt(R.styleable.CZSeekBar_max, DEF_PROGRESS_MAX)
        val progress = ta.getInt(R.styleable.CZSeekBar_progress, DEF_PROGRESS)
        val minusIcon = ta.getResourceId(R.styleable.CZSeekBar_minusBtnSrc, 0)
        val plusIcon = ta.getResourceId(R.styleable.CZSeekBar_plusBtnSrc, 0)
        val actionBtnSpace = ta.getBoolean(R.styleable.CZSeekBar_actionBtnSpace, true)
        if (minusIcon != 0) {
            binding.seekDownIv.setImageResource(minusIcon)
        }
        if (plusIcon != 0) {
            binding.seekUpIv.setImageResource(plusIcon)
        }
        ta.recycle()

        binding.seekUpIv.gone(!showActionBtn)
        binding.seekDownIv.gone(!showActionBtn)
        binding.spaceLeft.gone(!showActionBtn || !actionBtnSpace)
        binding.spaceRight.gone(!showActionBtn || !actionBtnSpace)

        if (showActionBtn) {
            binding.seekBarCore.onProgressReachesBoundaryListener = { boundary ->
                when (boundary) {
                    Boundary.MIN -> {
                        binding.seekDownIv.alpha = UI_DISABLE_ALPHA
                        binding.seekUpIv.alpha = 1F
                    }
                    Boundary.MAX -> {
                        binding.seekDownIv.alpha = 1F
                        binding.seekUpIv.alpha = UI_DISABLE_ALPHA
                    }
                    Boundary.MID -> {
                        binding.seekDownIv.alpha = 1F
                        binding.seekUpIv.alpha = 1F
                    }
                }
            }
        }

        binding.seekBarCore.apply {
            this.min = min
            this.max = max
            this.progress = progress
        }

        binding.seekUpIv.setOnDebounceClickListener {
            if (blockUserOperation) return@setOnDebounceClickListener
            binding.seekBarCore.updateProgress(binding.seekBarCore.progress + 1, true)
            onProgressChangeCompletedListener?.invoke(binding.seekBarCore.progress, true)

        }
        binding.seekDownIv.setOnDebounceClickListener {
            if (blockUserOperation) return@setOnDebounceClickListener
            binding.seekBarCore.updateProgress(binding.seekBarCore.progress - 1, true)
            onProgressChangeCompletedListener?.invoke(binding.seekBarCore.progress, true)
        }
    }
}