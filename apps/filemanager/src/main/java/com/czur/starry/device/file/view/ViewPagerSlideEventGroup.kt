package com.czur.starry.device.file.view

import android.animation.ArgbEvaluator
import android.animation.ObjectAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.InputDevice
import android.view.MotionEvent
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.FrameLayout
import androidx.core.view.forEach
import androidx.viewpager2.widget.ViewPager2
import com.czur.czurutils.log.logTagD
import kotlin.math.abs


private const val TAG = "ViewPagerEventGroup"
private const val MOVE_COUNTS = 4

class ViewPagerSlideEventGroup @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {

    private val viewPager: ViewPager2 by lazy {
        forEach {
            if (it is ViewPager2) {
                return@lazy it
            }
        }
        throw IllegalStateException("ViewPagerEventGroup must have a ViewPager2 child")
    }

    private var downX = 0F
    private var downY = 0F

    private var actionMove = false// 防止多次处理move事件

    // 如果图片放大后已经在边缘,move会少次触发就进入onInterceptTouchEvent,当做放大图片在边缘的判定条件(暂定4次,一般2次)
    private var actionMoveCounts = 0


    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        if (ev?.action == MotionEvent.ACTION_MOVE) {
            actionMoveCounts++
        }
        return super.dispatchTouchEvent(ev)
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        if (ev.action == MotionEvent.ACTION_DOWN) {
            downX = ev.x
            downY = ev.y
            actionMove = false
            actionMoveCounts = 0
        } else if (ev.action == MotionEvent.ACTION_MOVE) {
            if (ev.source == InputDevice.SOURCE_TOUCHSCREEN && !actionMove && actionMoveCounts < MOVE_COUNTS) {
                actionMove = true

                // 竖向滑动
                val dx = ev.x - downX
                if (dx < 0) {
                    // 向左滑动
                    logTagD(TAG, "向后翻页")
                    val firstCurrentItem = viewPager.currentItem
                    viewPager.currentItem = viewPager.currentItem + 1
                    val secondCurrentItem = viewPager.currentItem
                    if (firstCurrentItem == secondCurrentItem) {
                        moveView(-400f)
                    }
                } else {
                    // 向右滑动
                    logTagD(TAG, "向前翻页")

                    val firstCurrentItem = viewPager.currentItem
                    viewPager.currentItem = viewPager.currentItem - 1
                    val secondCurrentItem = viewPager.currentItem

                    if (firstCurrentItem == secondCurrentItem) {
                        moveView(400f)
                    }
                }

                return true
            }
        }

        return super.onInterceptTouchEvent(ev)
    }

    private fun moveView(end: Float) {
        // 当条件触发时，执行以下代码
        val anim = ObjectAnimator.ofFloat(this, "translationX", 0.0f, end, 0.0f)
        anim.duration = 700
        anim.interpolator = AccelerateDecelerateInterpolator()
        anim.start()

    }
}