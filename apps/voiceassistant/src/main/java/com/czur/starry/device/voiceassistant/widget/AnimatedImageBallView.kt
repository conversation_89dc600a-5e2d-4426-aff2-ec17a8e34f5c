package com.czur.starry.device.voiceassistant.widget

import android.animation.Animator
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.drawable.Drawable
import android.os.Handler
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.czur.czurutils.log.logTagD
private const val TAG = "AnimatedImageBallView"


/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2025/02/13
 *  Studio 球动画
 */

class AnimatedImageBallView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val handler = Handler()
    private var currentImageIndex = 0
    private val loopStart = 0
    private val loopEnd = 249
    private val packageName = context.packageName
    private var isQ2Series: Boolean = false
    private val drawableCache = mutableMapOf<Int, Drawable>() // 图片缓存


    // 初始化图片缓存
     fun initDrawableCache(isQ2Series: Boolean = false) {
         logTagD(TAG, "initDrawableCache isQ2Series: $isQ2Series")
        for (i in loopStart..loopEnd) {
            val resourceName = if (isQ2Series) {
                "q2_ball_%05d".format(i)
            } else {
                "ball_%05d".format(i)
            }
            val resourceId = context.resources.getIdentifier(resourceName, "drawable", packageName)
            val drawable = ContextCompat.getDrawable(context, resourceId)
            drawable?.let {
                drawableCache[i] = it
            }
        }
        logTagD(TAG, "drawableCache size: ${drawableCache.size} isQ2Series: $isQ2Series")
    }

    private fun updateImage() {
        drawableCache[currentImageIndex]?.let {
            background = it
        }

        // 更新索引
        if (currentImageIndex < loopEnd) {
            currentImageIndex++
        } else {
            // 循环到末尾，回到循环起始点
            currentImageIndex = loopStart
        }
    }


    fun initAnimation(isQ2Series: Boolean = false) {
        this.isQ2Series = isQ2Series
        visibility = VISIBLE
        updateImage()
        scaleXYAnimation(true)
    }

   private fun startAnimation(interval: Long = 40L) {
        handler.postDelayed({
            updateImage()
            startAnimation() // 递归调用保持动画
        }, interval)
    }

    fun stopAnimation() {
        handler.removeCallbacksAndMessages(null)
        scaleXYAnimation(false)
    }

    private fun scaleXYAnimation(isShow: Boolean = false) {
        lateinit var scaleXAnimator: ObjectAnimator
        lateinit var scaleYAnimator: ObjectAnimator
        if (isShow) {
            scaleXAnimator = ObjectAnimator.ofFloat(this, "scaleX", 0f, 1f)
            scaleYAnimator = ObjectAnimator.ofFloat(this, "scaleY", 0f, 1f)
        } else {
            scaleXAnimator = ObjectAnimator.ofFloat(this, "scaleX", 0f)
            scaleYAnimator = ObjectAnimator.ofFloat(this, "scaleY", 0f)
        }
        // 设置动画的持续时长为500毫秒
        scaleXAnimator.duration = 500
        scaleYAnimator.duration = 500
        // 为动画添加监听器，以便在动画结束时隐藏视图
        val animatorListener = object : Animator.AnimatorListener {
            override fun onAnimationStart(p0: Animator) {
            }

            override fun onAnimationEnd(p0: Animator) {
                if (!isShow) {
                    visibility = GONE
                }else {
                    startAnimation()
                }
            }

            override fun onAnimationCancel(p0: Animator) {
            }

            override fun onAnimationRepeat(p0: Animator) {
            }

        }

        // 为缩放动画设置监听器
        scaleXAnimator.addListener(animatorListener)
        scaleYAnimator.addListener(animatorListener)

        scaleXAnimator.start()
        scaleYAnimator.start()
    }


    // 重写onDetachedFromWindow以在View被销毁时停止动画
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        stopAnimation()
    }
}