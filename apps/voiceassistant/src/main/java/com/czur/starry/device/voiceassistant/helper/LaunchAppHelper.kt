package com.czur.starry.device.voiceassistant.helper

import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.storage.StorageManager
import android.os.storage.VolumeInfo
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_BYOM_ESHARE_GUIDE
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_BYOM_GUIDE
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_HDMI
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_NOTICE_CENTER
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_SCREEN_SHARE
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_TRANSCRIPTION
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_WRITE_PAD
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAME
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_HDMI_AIRPLAY_OPEN
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.view.getVolumes
import com.czur.starry.device.hdmilib.HDMIMonitor
import com.czur.starry.device.voiceassistant.R
import com.czur.starry.device.voiceassistant.VoiceAssistantService.Companion.TAG
import com.czur.starry.device.voiceassistant.util.FileSetUtil.bootTranscription
import java.nio.file.WatchService

/**
 *  author : WangHao
 *  time   :2025/01/24
 *  执行打开应用页面功能
 */

//打开应用返回状态
enum class LaunchResult {
    SUCCESS,               // 正常
    NOT_INSTALL,          // 异常或不存在
    NOT_FOUND,           // 页面找不到
}

class LaunchAppHelper(private val context: Context, private val packageManager: PackageManager) {

    // 通用系统名单
    private val commonSystemPkgList = listOf(
        "com.czur.starry.device.starrypad",
        "com.czur.starry.device.localmeetingrecord",
        "com.czur.starry.device.appstore",
        "com.czur.starry.device.meeting",
        "com.czur.starry.device.file",
        "com.czur.starry.device.personalcenter",
        "com.czur.starry.device.wallpaperdisplay",
        "com.czur.starry.device.settings",
        "com.czur.starry.device.hdmiin",
        "com.czur.starry.device.transcription"
    )

    val systemPkgList by lazy {
        val baseSystemPkgList = commonSystemPkgList
        when (Constants.starryHWInfo.model) {
            StarryModel.StudioModel.StudioSPlus -> baseSystemPkgList.filter { it != "com.czur.starry.device.wallpaperdisplay" }
            // 处理其他机型情况
            else -> baseSystemPkgList
        }
    }

    /**
     * 通过名字打开应用
     */
    fun launchAppByName(
        appName: String,
        pageMenu: String = "",
        pageList: MutableList<String>
    ): LaunchResult {

        when {
            context.getString(R.string.app_name_byom).contains(appName) -> {
                return startAppByAction(ACTION_BOOT_BYOM_GUIDE)
            }
            context.getString(R.string.app_name_byom_wireless).contains(appName) -> {
                return startAppByAction(ACTION_BOOT_BYOM_ESHARE_GUIDE)
            }

            context.getString(R.string.app_name_hdmi) == appName -> {
                if (getBooleanSystemProp(KEY_HDMI_AIRPLAY_OPEN, false)) {
                    sendBroadCastToMirror(context, true)
                    return LaunchResult.SUCCESS
                } else {
                    return startAppByAction(ACTION_BOOT_HDMI)
                }

            }

            context.getString(R.string.app_name_screenshare) == appName -> {
                return startAppByAction(ACTION_BOOT_SCREEN_SHARE)
            }

            context.getString(R.string.app_name_transcription).contains(appName) -> {
                startAppByAction(ACTION_BOOT_TRANSCRIPTION)
                return LaunchResult.SUCCESS
            }

            context.getString(R.string.app_name_message) == appName -> {
                if (!UserHandler.isLogin) {
                    // 提示未登录
                }
                return startAppByAction(ACTION_BOOT_NOTICE_CENTER)
            }

            context.getString(R.string.app_name_witepad).contains(appName) -> {
                return startAppByAction(ACTION_BOOT_WRITE_PAD)
            }

            else -> {

                val pageMenuMap = mapOf(
                    "WiFi" to context.getString(R.string.page_menu_wifi),
                    context.getString(R.string.page_menu_network) to context.getString(R.string.page_menu_wifi),
                    context.getString(R.string.page_menu_personal) to context.getString(R.string.page_menu_wallpaper),
                    context.getString(R.string.setting_camera) to context.getString(R.string.setting_screen_scene),
                    context.getString(R.string.voice_command_sleep_wakeup) to context.getString(R.string.page_menu_sleep_wakeup)
                )
                // 根据映射关系获取跳转参数，若没有匹配则使用原始 pageMenu
                val targetPageMenu = pageMenuMap[pageMenu] ?: pageMenu

                //如果是设置,则判断设置页面是否存在
                if (appName == context.getString(R.string.app_name_settings) && targetPageMenu != context.getString(R.string.intent_nothing)) {
                    if (!pageList.contains(pageMenu)) {
                        startAppFromPackage(appName)
                        return LaunchResult.NOT_FOUND
                    }
                }
                if (appName == context.getString(R.string.app_name_file) && targetPageMenu == context.getString(R.string.app_name_file_usb)) {
                     if (!isUsbMounted()) {
                         startAppFromPackage(appName, targetPageMenu)
                         return LaunchResult.NOT_FOUND
                    }
                }
                return startAppFromPackage(appName, targetPageMenu)

            }
        }
        return LaunchResult.NOT_INSTALL

    }

    /**
     * 判断是否有 U 盘挂载
     * @return 若有 U 盘挂载返回 true，否则返回 false
     */
    fun isUsbMounted(): Boolean {
        val storageManager = context.getSystemService(Context.STORAGE_SERVICE) as StorageManager
        val volumes = storageManager.getVolumes()
        return volumes.any { volumeInfo ->
            // 判断是否为公共类型 U 盘且已挂载可读
            volumeInfo.type == VolumeInfo.TYPE_PUBLIC &&
                    volumeInfo.getDisk()?.isUsb == true &&
                    volumeInfo.isMountedReadable &&
                    volumeInfo.path != null
        }
    }

    /**
     *先根据包名获取启动入口，没有再根据action打开应用
     */
    private fun startAppFromPackage(appName: String, pageMenu: String = ""): LaunchResult {
        val packages = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
            .filter { appInfo ->
                val isSystemApp = (appInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0
                // 如果不是系统应用，或者是系统应用但包名在 commonSystemPkgList 中，则保留
                !isSystemApp || systemPkgList.contains(appInfo.packageName)
            }
        val exactMatches = mutableListOf<ApplicationInfo>()
        val partialMatches = mutableListOf<ApplicationInfo>()

        for (pkg in packages) {
            val label = packageManager.getApplicationLabel(pkg).toString().lowercase()
            when {
                label == appName.lowercase() -> exactMatches.add(pkg)
                label.contains(appName.lowercase()) -> partialMatches.add(pkg)
            }
        }

        val matchingPackages = exactMatches.ifEmpty { partialMatches }

        if (matchingPackages.isEmpty()) {
            logTagD(TAG, "====没有该应用:${appName}")
            return LaunchResult.NOT_INSTALL
        }

        logTagD(TAG, "====matchingPackages.size:${matchingPackages.size}")

        when (matchingPackages.size) {
            1 -> {
                val packageName = matchingPackages.first().packageName
                val intent = packageManager.getLaunchIntentForPackage(packageName)
                if (intent != null) {
                    if (pageMenu.isNotEmpty()) {
                        intent.putExtra(BOOT_KEY_PAGE_MENU_NAME, pageMenu)
                    }
                    intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                    context.startActivity(intent)
                } else {
                    // 成者应用部分没有设置启动页入口
                    startAppByAction("$packageName.BOOT_APP", pageMenu)
                }
            }

            in 2..Int.MAX_VALUE -> matchingPackages.firstOrNull { it.packageName.contains("czur") }
                ?.let {
                    val intent = packageManager.getLaunchIntentForPackage(it.packageName)
                    if (intent != null) {
                        if (pageMenu.isNotEmpty()) {
                            intent.putExtra(BOOT_KEY_PAGE_MENU_NAME, pageMenu)
                        }
                        intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
                        context.startActivity(intent)
                    } else {
                        // 成者应用部分没有设置启动页入口
                        startAppByAction("${it.packageName}.BOOT_APP", pageMenu)
                    }
                }
        }
        return LaunchResult.SUCCESS
    }

    /**
     * 通过action打开应用
     */
    private fun startAppByAction(
        bootAction: String,
        pageMenu: String = ""
    ): LaunchResult {
        try {
            val intent = Intent().apply {
                action = bootAction
                if (pageMenu.isNotEmpty()) {
                    putExtra(BOOT_KEY_PAGE_MENU_NAME, pageMenu)
                }
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
            return LaunchResult.SUCCESS
        } catch (e: Exception) {
            logTagE(TAG, "startAppByAction error:${e.message}")
            e.printStackTrace()
        }
        return LaunchResult.NOT_INSTALL
    }

    /**
     * 发送广播通知投屏
     */
    fun sendBroadCastToMirror(context: Context?, hdmiIn: Boolean) {
        logTagV(TAG, "给宜享发送hdmi广播 hdmiIn:${hdmiIn}")
        val intent = Intent(HDMIMonitor.ACTION_HDMI_STATE).apply {
            putExtra(HDMIMonitor.ACTION_HDMI_STATE_KEY, hdmiIn)
            setPackage(HDMIMonitor.EAIRPLAY_PACKAGE_NAME)
        }
        context?.sendBroadcast(intent)
    }

}