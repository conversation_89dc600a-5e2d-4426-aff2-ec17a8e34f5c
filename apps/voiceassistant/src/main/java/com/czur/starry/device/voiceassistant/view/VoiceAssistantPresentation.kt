package com.czur.starry.device.voiceassistant.view

import android.app.Presentation
import android.content.Context
import android.os.Bundle
import android.view.Display
import androidx.lifecycle.LifecycleCoroutineScope
import com.czur.starry.device.voiceassistant.R
import com.czur.starry.device.voiceassistant.widget.AnimatedImageBallView
import com.czur.starry.device.voiceassistant.widget.AnimatedImageBgView
import com.czur.starry.device.voiceassistant.widget.AnimatedImageCloudsView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2025/05/12
 */

class VoiceAssistantPresentation(
    context: Context,
    display: Display,
    private val lifecycleScope: LifecycleCoroutineScope
) : Presentation(context, display) {

    // 声明视图组件
    private lateinit var backgroundView: AnimatedImageBgView
    private lateinit var ballView: AnimatedImageBallView
    private lateinit var cloudsView: AnimatedImageCloudsView

    // 动画控制Job
    private var animationJob: Job? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.voice_assistant_presentation)

        // 初始化视图组件（必须使用Presentation的Context）
        backgroundView = findViewById(R.id.backgroundView)
        ballView = findViewById(R.id.ballView)
        cloudsView = findViewById(R.id.cloudsView)

        backgroundView.initDrawableCache()
        ballView.initDrawableCache()
        cloudsView.initDrawableCache()

    }

    fun startAnimations() {
        animationJob = lifecycleScope.launch(Dispatchers.Main) {
            // 第一阶段：背景动画
            backgroundView.initAnimation()
            delay(150) // 使用协程精确控制时序

            // 第二阶段：球体动画
            ballView.initAnimation()
            delay(1000) // 可根据需要调整延时

            // 第三阶段：云朵动画
            cloudsView.initAnimation()
        }
    }

    fun stopAnimations() {
        lifecycleScope.launch(Dispatchers.Main) {
            ballView.stopAnimation()
            cloudsView.stopAnimation()
            delay(450)//目前调整合适
            backgroundView.stopAnimation()
            dismiss()
        }
    }

    fun setScaleYFactor(value: Float) {
        cloudsView.triggerWaveEffect(value)
    }


}

