package com.czur.starry.device.voiceassistant.util

import android.content.Context
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.voiceassistant.R.string
import okio.AsyncTimeout.Companion.condition

/**
 *  author : <PERSON>Hao
 *  time   :2025/05/07
 */


object FileUtil {

    /**
     * 根据设备型号获取符合条件的 nameRes 列表
     * @return 符合条件的 nameRes 列表
     */
    fun getFilteredNameResList(context: Context, pageMenuList: MutableList<String>): List<String> {
        // 定义 nameRes 与型号判断条件的映射
        val nameResMap = mapOf(
            context.getString(string.hdmi_cec) to {
                when (Constants.starryHWInfo.series) {
                    StudioSeries, Q2Series -> true
                    else -> false
                }
            },
            context.getString(string.focusing) to {
                when (Constants.starryHWInfo.series) {
                    StudioSeries -> false
                    else -> true
                }
            },
            context.getString(string.sub_menu_title_screen_brightness) to {
                Constants.starryHWInfo.hasTouchScreen
            },
            context.getString(string.sub_menu_title_screen) to {
                when (Constants.starryHWInfo.series) {
                    StudioSeries -> false
                    else -> true
                }
            },
            context.getString(string.keystone_correction) to {
                when (Constants.starryHWInfo.series) {
                    StudioSeries -> false
                    else -> true
                }
            },
            context.getString(string.boot_start) to {
                when (Constants.starryHWInfo.series.model) {
                    StarryModel.Q1Model.Q1,
                    StarryModel.Q1Model.Q1Pro,
                    StarryModel.Q1Model.Q1S,
                    StarryModel.Q2Model.Q2,
                    StarryModel.Q2Model.Q2Pro,
                    StarryModel.StudioModel.Studio,
                    StarryModel.StudioModel.StudioPro,
                    StarryModel.StudioModel.StudioS,
                    StarryModel.StudioModel.StudioSPro
                        -> false

                    else -> true
                }
            },
            context.getString(string.sub_menu_title_wallpaper_setting) to {
                when (Constants.starryHWInfo.model) {
                    StarryModel.Q1Model.Q1,
                    StarryModel.Q1Model.Q1Pro,
                    StarryModel.StudioModel.Studio,
                    StarryModel.StudioModel.StudioPro,
                    StarryModel.StudioModel.StudioSPlus,
                    StarryModel.Q2Model.Q2,
                    StarryModel.Q2Model.Q2Pro -> false

                    else -> true
                }
            },
            context.getString(string.sub_menu_title_custom_shortcut_keys) to {
                when (Constants.starryHWInfo.series) {
                    StudioSeries -> true    // 只有Studio系列显示(Studio s plus 不显示)
                    else -> false
                }
            },
            context.getString(string.sub_menu_title_lang) to {
                Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas
            }
        )

        nameResMap.forEach { (nameRes, condition) ->
            if (condition()) {
                pageMenuList.add(nameRes)
            }
        }

        return pageMenuList
    }


}