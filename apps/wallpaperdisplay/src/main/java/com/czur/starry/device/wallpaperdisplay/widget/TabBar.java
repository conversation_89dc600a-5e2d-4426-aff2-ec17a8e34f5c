package com.czur.starry.device.wallpaperdisplay.widget;

import static androidx.constraintlayout.widget.ConstraintSet.CHAIN_SPREAD_INSIDE;
import static androidx.constraintlayout.widget.ConstraintSet.PARENT_ID;

import android.content.Context;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.czur.starry.device.baselib.utils.ConstraintUtil;
import com.czur.starry.device.wallpaperdisplay.R;

import androidx.annotation.Nullable;


public class TabBar extends ConstraintLayout implements View.OnClickListener {
    private String[] titles;
    private static final int ID_OFF_SET = 1000;
    private ConstraintUtil constraintUtil;
    private int selIndex = 0;
    private static final float TEXT_SIZE_SCALE = 1f;
    private OnSelChangeListener listener;

    public TabBar(Context context) {
        super(context);
        init();
    }

    public TabBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public TabBar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        constraintUtil = new ConstraintUtil(this);
        updateViews();
    }

    public void setTitles(String[] titles) {
        this.titles = titles;
        updateViews();
        selIndex = 0;
        updateSelUI(selIndex);
    }

    /**
     * 设置标签改变监听
     *
     * @param listener 选中标题改变的监听, 如果传入null,则表示清理回调
     */
    public void setOnSelChangeListener(@Nullable OnSelChangeListener listener) {
        this.listener = listener;
    }

    private void updateViews() {
        removeAllViews();
        if (titles == null) {
            return;
        }


        for (int i = 0; i < titles.length; i++) {
            String title = titles[i];
            TextView textView = (TextView) LayoutInflater.from(getContext())
                    .inflate(R.layout.item_select_tab, this, false);
            textView.setId(View.generateViewId());

            textView.setTag(i + ID_OFF_SET);
            textView.setOnClickListener(this);

            textView.setText(title);
            final int theIndex = i;
            // 鼠标事件
            textView.setOnHoverListener((v, event) -> {
                if (selIndex == theIndex) {
                    return false;
                }
                switch (event.getAction()) {
                    case MotionEvent.ACTION_HOVER_ENTER:
                        textView.setTextColor(getResources().getColor(R.color.colorWhite));
                        return true;
                    case MotionEvent.ACTION_HOVER_EXIT:
                        textView.setTextColor(getResources().getColor(R.color.colorWhiteHint));
                        return true;
                }
                return false;
            });

            addView(textView);
        }

        ConstraintUtil.ConstraintBegin begin = constraintUtil.begin();

        begin.commit();
        for (int i = 0; i < titles.length; i++) {
            if (i == 0) {
                begin.leftToLeftOf(getChildAt(i).getId(), PARENT_ID);
            } else {
                begin.leftToRightOf(getChildAt(i).getId(), getChildAt(i - 1).getId());
            }

            if (i == titles.length - 1) {
                begin.rightToRightOf(getChildAt(i).getId(), PARENT_ID);
            } else {
                begin.rightToLeftOf(getChildAt(i).getId(), getChildAt(i + 1).getId());
            }
            updateNormalUI(i);
        }
        begin.chainHorizontal(getChildAt(0).getId(), CHAIN_SPREAD_INSIDE);
        begin.commit();

    }

    private void updateSelUI(int selIndex) {
        TextView textView = (TextView) getChildAt(selIndex);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, 30 * TEXT_SIZE_SCALE);
        textView.setTextColor(getResources().getColor(R.color.colorWhite));
    }

    private void updateNormalUI(int normalIndex) {
        TextView textView = (TextView) getChildAt(normalIndex);
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, 24 * TEXT_SIZE_SCALE);
        textView.setTextColor(getResources().getColor(R.color.colorWhiteHint));
    }

    @Override
    public void onClick(View v) {
        int id = (int) v.getTag();
        int clickIndex = id - ID_OFF_SET;
        if (clickIndex != selIndex) {
            updateNormalUI(selIndex);
            updateSelUI(clickIndex);
            if (listener != null) {
                listener.onSelChange(clickIndex);
            }
        }

        selIndex = clickIndex;
    }


    public interface OnSelChangeListener {
        void onSelChange(int selPos);
    }

    public void setTextViewBar(int id) {
        int clickIndex = id - ID_OFF_SET;
        if (clickIndex != selIndex) {
            updateNormalUI(selIndex);
            updateSelUI(clickIndex);
            if (listener != null) {
                listener.onSelChange(clickIndex);
            }
        }

        selIndex = clickIndex;
    }

}
