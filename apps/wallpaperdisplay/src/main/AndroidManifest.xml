<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="com.czur.starry.contentProvider.rw.sp" />
    <uses-permission android:name="android.permission.INTERNET" /> <!-- 阿里云OSS要求添加的权限 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <application
        android:name=".App"
        android:icon="@mipmap/icon"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">


        <activity
            android:name=".view.activity.MainActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection"
            android:exported="true"
            android:theme="@style/BaseAppTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".view.activity.PreviewActivity"
            android:configChanges="fontScale|keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|navigation|layoutDirection"
            android:exported="true"
            android:theme="@style/BaseAppTheme" />

        <receiver
            android:name=".view.activity.PlayWallPaperReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.czur.alarm.action.wallpaper" />
                <action android:name="com.android.action.CZUR_DISPLAY_WALLPAPER" />
                <action android:name="com.android.action.CZUR.BTB.LEAVE" />
            </intent-filter>
        </receiver>

    </application>

</manifest>