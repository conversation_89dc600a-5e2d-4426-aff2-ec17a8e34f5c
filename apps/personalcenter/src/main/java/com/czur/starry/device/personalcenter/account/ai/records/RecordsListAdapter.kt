package com.czur.starry.device.personalcenter.account.ai.records

import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.getTimeStr
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.bean.RecordDetails
import com.czur.uilib.choose.CZImageCheckBox

/**
 * Created by sqj on 2025/7/16
 */
class RecordsListAdapter : BaseDifferAdapter<RecordDetails>() {
    // 选择项变化的回调
    var onSelectionChangedListener: (() -> Unit)? = null

    // 创建ViewHolder
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_ai_trans_record, parent)
    }

    // 绑定数据到ViewHolder
    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: RecordDetails) {
        // 记录次数
        when(itemData.memberType){
            0->{//过期
                holder.setText(getString(R.string.str_record_lost_over_time), R.id.recordNameTv)
                holder.setText(getString(R.string.str_record_lost_times,itemData.name), R.id.recordNumberTv)
            }

            1->{// 包月 计次使用
                holder.setText(getString(R.string.str_record_lost_times_str), R.id.recordNameTv)
                holder.setText(getString(R.string.str_record_lost_times,"1"), R.id.recordNumberTv)
            }

            2->{// 计时
                // 时间格式为 hh:mm:ss, 如果没到小时,不显示小时, 没到分钟,显示00
                val duration = itemData.duration
                val hours = duration / ONE_HOUR
                val minutes = (duration % ONE_HOUR) / ONE_MIN
                val seconds = duration % ONE_MIN / 1000
                val timeStr = getString(R.string.str_record_lost_time,hours, minutes, seconds)

                holder.setText(getString(R.string.str_record_lost_time_str), R.id.recordNameTv)
                holder.setText(timeStr, R.id.recordNumberTv)
            }
            else -> {
                holder.setText("", R.id.recordNameTv)
                holder.setText("", R.id.recordNumberTv)
            }
        }

        if (itemData.status == 1){//进行中
            holder.setText(getString(R.string.str_record_counting), R.id.recordNumberTv)
        }

        // 设置创建时间
        val time = itemData.createTime.toLongOrNull() ?: 0
        val timeStr = getTimeStr("yyyy/MM/dd HH:mm:ss", time)
        holder.setText(timeStr, R.id.recordTimeTv)

    }

    // 判断两个item是否是同一个
    override fun areItemsTheSame(oldItem: RecordDetails, newItem: RecordDetails): Boolean {
        return oldItem.id == newItem.id
    }

    // 判断两个item的内容是否相同
    override fun areContentsTheSame(oldItem: RecordDetails, newItem: RecordDetails): Boolean {
        // 特别注意选中状态的变化
        return oldItem == newItem
    }
}