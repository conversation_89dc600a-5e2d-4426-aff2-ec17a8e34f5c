package com.czur.starry.device.personalcenter.account.ai.records

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.network.core.util.createMiaoHttpException
import com.czur.starry.device.personalcenter.bean.RecordDetails
import com.czur.starry.device.personalcenter.bean.RecordInfo
import com.czur.starry.device.personalcenter.net.IAccountServer
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.withContext
import kotlin.collections.filter

/**
 * Created by sqj on 2025/7/16
 */
class AiUseRecordsViewModel(application: Application) : AndroidViewModel(application) {
    private val aiTransServer: IAccountServer by lazy {
        HttpManager.getService()
    }

    private val gson = Gson()

    val _serverRecordListFlow = MutableStateFlow<List<RecordDetails>>(listOf())

    // 分页相关状态
    private val _isLoadingFlow = MutableStateFlow(false)
    val isLoadingFlow = _isLoadingFlow.asStateFlow()

    private val _hasMoreDataFlow = MutableStateFlow(true)
    val hasMoreDataFlow = _hasMoreDataFlow.asStateFlow()

    private var currentPage = 0
    private val pageSize = 50

    /**
     * 刷新记录列表（重置到第一页）
     */
    suspend fun refreshRecordList(): Result<List<RecordDetails>> {
        if (_isLoadingFlow.value) return Result.success(emptyList())

        _isLoadingFlow.value = true
        currentPage = 1
        _hasMoreDataFlow.value = true

        return withContext(Dispatchers.IO) {
            try {
                val result = aiTransServer.getRecordList(
                    page = currentPage.toString(),
                    size = pageSize.toString()
                )
                if (result.isSuccess) {
                    val resultData = result.body.data
                    _serverRecordListFlow.value = resultData

                    // 如果返回的数据少于页面大小，说明没有更多数据了
                    if (resultData.size < pageSize) {
                        _hasMoreDataFlow.value = false
                    }

                    Result.success(resultData)
                } else {
                    Result.failure(createMiaoHttpException(result))
                }
            } catch (e: Exception) {
                Result.failure(e)
            } finally {
                _isLoadingFlow.value = false
            }
        }
    }

    /**
     * 加载更多数据
     */
    suspend fun loadMoreRecords(): Result<List<RecordDetails>> {
        if (_isLoadingFlow.value || !_hasMoreDataFlow.value) {
            return Result.success(emptyList())
        }

        _isLoadingFlow.value = true
        currentPage++

        return withContext(Dispatchers.IO) {
            try {
                val result = aiTransServer.getRecordList(
                    page = currentPage.toString(),
                    size = pageSize.toString()
                )
                if (result.isSuccess) {
                    val resultData = result.body.data
                    // 解析JSON字符串
                    val currentList = _serverRecordListFlow.value.toMutableList()
                    currentList.addAll(resultData)
                    _serverRecordListFlow.value = currentList

                    // 如果返回的数据少于页面大小，说明没有更多数据了
                    if (resultData.size < pageSize) {
                        _hasMoreDataFlow.value = false
                    }

                    Result.success(resultData)
                } else {
                    // 加载失败时回退页码
                    currentPage--
                    Result.failure(createMiaoHttpException(result))
                }
            } catch (e: Exception) {
                // 加载失败时回退页码
                currentPage--
                Result.failure(e)
            } finally {
                _isLoadingFlow.value = false
            }
        }
    }

    /**
     * 兼容旧方法
     */
    suspend fun getRecordList(page: String): Result<List<RecordDetails>> {
        return refreshRecordList()
    }

    fun updateRecordList(data: List<RecordDetails>) {
        _serverRecordListFlow.value = data
    }
}

