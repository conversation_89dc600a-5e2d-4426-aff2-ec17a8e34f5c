package com.czur.starry.device.startupsettings.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.startupsettings.R
import kotlin.math.pow

/**
 * Created by 陈丰尧 on 2022/9/5
 */
class TouchSliderView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "TouchSliderView"
        private const val SLIDE_SUCC_THRESHOLD = 10F  // 滑动成功的阈值
        private const val ANIM_DURATION_MAX = 500L
    }

    private val slideIv by lazy { findViewById(R.id.sliderIv) as ImageView }
    private val hintTv by lazy { findViewById(R.id.slideHintTv) as TextView }

    private var slideLeft: Float = 0F

    private var dDownX: Float = -1F  // 按下位置 与滑块左侧位置的差值

    private var slidePercent: Float = 0F // 滑动的百分比
        set(value) {
            field = value
            updateTextAlpha()
        }

    // 可以滑动的最大距离
    private val slideMax: Float by lazy {
        width.toFloat() - paddingLeft - paddingRight - slideIv.width
    }

    var onSlideSuccListener: (() -> Unit)? = null
    private var slideSuccess = false    // 是否滑动成功了
        set(value) {
            field = value
            if (field) {
                onSlideSuccListener?.invoke()
            }
        }

    init {
        inflate(context, R.layout.widget_touch_slider, this)
        slideLeft = paddingLeft.toFloat()   // 滑块的左侧
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (event == null) return super.onTouchEvent(event)

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                val downX = event.x
                if (downX in slideLeft..slideLeft + slideIv.width) {
                    dDownX = downX - slideLeft
                    return true
                }
            }
            MotionEvent.ACTION_MOVE -> {
                var targetX = event.x - dDownX
                if (targetX < 0) targetX = 0F
                if (targetX > slideMax) targetX = slideMax
                // 在范围内滑动
                slideIv.translationX = targetX  // 移动滑块
                updateSlideValue()  // 重新记录左侧值
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (!slideSuccess) {
                    startReboundAnim()
                }
            }
        }

        return super.onTouchEvent(event)
    }

    private fun updateSlideValue() {
        slideLeft = slideIv.left.toFloat() + slideIv.translationX
        slidePercent = slideIv.translationX / slideMax
        slideSuccess = slideIv.translationX + SLIDE_SUCC_THRESHOLD >= slideMax
    }


    private fun updateTextAlpha() {
        // 透明度使用一个2此函数, 让他更快的变成全透明
        // (x-1)^4 下面是化简后的结果
        val showPercent = (slidePercent-1).pow(2)
        hintTv.alpha = showPercent
    }

    private fun startReboundAnim() {
        logTagV(TAG, "开启回弹动画")
        // 根据滑动的百分比,计算回弹的时间
        val duration = (slidePercent * ANIM_DURATION_MAX).toLong()
        if (duration <= 0) return
        slideIv.animate()
            .translationX(0F)
            .setDuration(duration)
            .setUpdateListener {
                updateSlideValue()
            }
            .start()
    }
}