package com.czur.starry.device.startupsettings

import com.czur.czurutils.log.logTagW
import com.czur.czurutils.log.logTagD
import android.os.LocaleList
import android.util.Log
import com.czur.starry.device.baselib.utils.SettingHandler
import java.util.*

/**
 * Created by 陈丰尧 on 2/7/21
 * 语言相关功能
 */
object LanguageManager {
    private const val TAG = "LanguageManager"

    val hansCZ: Locale = Locale.Builder().setLanguage("zh")
        .setScript("Hans")
        .setRegion("CN")
        .build()
    val eng: Locale = Locale.US


    /**
     * 更新系统语言
     */
    fun updateLanguage(locale: Locale) {
        val ll = LocaleList(locale)
        LocaleList.setDefault(ll)
        try {
            val updateMethod = Class.forName("com.android.internal.app.LocalePicker")
                .getMethod("updateLocales", LocaleList::class.java)
            updateMethod.invoke(null, ll)
        } catch (e: Exception) {
            logTagW(TAG, "设置系统语言错误", tr = e)
        }

        logTagD(TAG, "code:${SettingHandler.languageCode}")
    }

}