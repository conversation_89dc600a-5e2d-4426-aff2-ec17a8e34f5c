package com.czur.starry.device.startupsettings

import android.annotation.SuppressLint
import android.app.ActivityOptions
import android.app.AlarmManager
import android.bluetooth.BluetoothAdapter
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.hardware.display.DisplayManager
import android.media.AudioManager
import android.media.MediaPlayer
import android.os.Bundle
import android.provider.Settings
import android.util.DisplayMetrics
import android.view.KeyEvent
import android.widget.ImageView
import androidx.fragment.app.commit
import com.czur.czurutils.extension.platform.newTask
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.BaseActivity
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.Constants.TOUCH_PAD_NAMES
import com.czur.starry.device.baselib.common.Constants.VOLUME_CALL_DEF
import com.czur.starry.device.baselib.common.Constants.VOLUME_MEDIA_DEF
import com.czur.starry.device.baselib.common.Constants.starryHWInfo
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.has
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.generateNewStarryDeviceID
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.startupsettings.LanguageManager.eng
import com.czur.starry.device.startupsettings.LanguageManager.hansCZ
import com.czur.starry.device.startupsettings.LanguageManager.updateLanguage
import com.czur.starry.device.startupsettings.fragment.TouchPadConnFragment
import com.eshare.serverlibrary.api.EShareServerSDK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlin.math.round

private const val SHOW_DURATION = 2000L
private const val TAG = "WelcomeActivity"
private const val SHOW_MAX_VOLUME = 15

class WelcomeActivity : BaseActivity() {
    companion object {
        private const val KEY_SHOW_BOOT_IMG = "system.czur.show_boot_img"
    }

    override fun getLayout() = R.layout.activity_welcome

    private val audioManager by lazy {
        getSystemService(AudioManager::class.java)
    }

    override fun initViews() {
        super.initViews()
        if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas) {
            // 海外版
            findViewById<ImageView>(R.id.welcomeIv).setImageResource(R.drawable.welcome_bg_overseas)
        } else if (Constants.versionIndustry == VersionIndustry.PARTY_BUILDING) {
            logTagD(TAG, "党建版本")
            findViewById<ImageView>(R.id.welcomeIv).setImageResource(R.drawable.welcome_bg_party_building)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        //加载U盘
        var usbMount = FactoryTestHelper.USBBootType.NONE

        launch {
            if (starryHWInfo.series == StudioSeries) {
                bootSmallRoundScreen()
            }

            //设置语言有线
            val lang = async {
                setDefLanguage()
            }

            //等待2s确认厂测启动U盘
            for (i in 0..2) {
                delay(ONE_SECOND)
                logTagV(TAG, "检测厂测程序第${i + 1}次")
                usbMount = withContext(Dispatchers.IO) {
                    FactoryTestHelper.getUSBBootType()
                }
                if (usbMount != FactoryTestHelper.USBBootType.NONE) break
            }
            if (usbMount != FactoryTestHelper.USBBootType.NONE) {
                logTagV(TAG, "checkResult:$usbMount")
                FactoryTestHelper.bootUSBTestApp(this@WelcomeActivity, usbMount)
                return@launch
            }


            //未插入启动厂测U盘
            val volume = async {
                // 设置默认声音大小
                setVolumeToDef()
            }

            val delay = async {
                delay(SHOW_DURATION)
            }
            // 初始化投屏信息
            val share = async {
                doWithoutCatch {
                    initScreenShare()
                }
            }
            val timeZone = async {
                setDefTimeZone()
            }

            val generateID = async {
                generateNewStarryDeviceID()
            }

            val playSound = async {
                playSound()
            }

            val bootCZKeyStone = async {
                bootCZKeyStone()
            }
            val checkTouchPad = async {
                checkNeedGuideTouchPadConn()
            }
            val checkAirPlaneMode = async {
                checkAirplaneMode()
            }
            volume.await()
            lang.await()
            delay.await()
            share.await()
            timeZone.await()
            generateID.await()
            playSound.await()
            bootCZKeyStone.await()
            checkAirPlaneMode.await()

            // 启动监控服务
            logTagV(TAG, "启动监控服务")
            startService(Intent().apply {
                setPackage("com.czur.starry.device.noticecenter")
                setClassName(
                    "com.czur.starry.device.noticecenter",
                    "com.czur.starry.device.noticecenter.WatchService"
                )
            })

            // 任务都完成后, 跳转到下一页
            checkTouchPad.await()
                .yes {
                    logTagV(TAG, "没有连接过触控板")
                    showTouchPadConnGuidePage()
                }.otherwise {
                    logTagV(TAG, "已经连接过触控板")
                    bootStartUpActivity()
                }
        }
    }

    private fun bootSmallRoundScreen() {
        logTagD(TAG, "启动小圆屏")
        val intent = Intent().apply {
            component = ComponentName("com.czur.starry.device.smallroundscreen", "com.czur.starry.device.smallroundscreen.AnimationActivity")
        }.newTask()

        val displayManager = getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val displays = displayManager.displays
        var targetDisplayId = -1
        for (display in displays) {
            val metrics = DisplayMetrics()
            display.getRealMetrics(metrics)
            if (metrics.widthPixels == 360 && metrics.heightPixels == 360) {
                targetDisplayId = display.displayId
                break
            }
        }

        if (targetDisplayId != -1) {
            val options = ActivityOptions.makeBasic().apply {
                launchDisplayId = targetDisplayId
            }
            logTagD(TAG, "找到了尺寸为 360x360 的屏幕")
            startActivity(intent, options.toBundle())
        } else {
            logTagD(TAG, "没有找到尺寸为 360x360 的屏幕")
        }
    }

    private fun showTouchPadConnGuidePage() {
        supportFragmentManager.commit {
            add(R.id.welcomeRootFl, TouchPadConnFragment())
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        // 拦截返回键
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    /**
     * 检查是否处于飞行模式
     */
    private suspend fun checkAirplaneMode() = withContext(Dispatchers.IO) {
        val isAirplaneModeOn = Settings.Global.getInt(
            contentResolver,
            Settings.Global.AIRPLANE_MODE_ON, 0
        ) != 0
        if (isAirplaneModeOn) {
            logTagW(TAG, "飞行模式已开启")
            Settings.Global.putInt(
                contentResolver,
                Settings.Global.AIRPLANE_MODE_ON, 0
            )
            val intent = Intent(Intent.ACTION_AIRPLANE_MODE_CHANGED)
            intent.putExtra("state", false)
            sendBroadcast(intent)
        } else {
            logTagV(TAG, "飞行模式未开启")
        }
    }

    /**
     * 检查是否需要引导连接触控板
     */
    @SuppressLint("MissingPermission")
    private suspend fun checkNeedGuideTouchPadConn(): Boolean = withContext(Dispatchers.IO) {
        logTagV(TAG, "检查是否需要引导连接触控板")
        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            logTagV(TAG, "军工版本, 不需要引导连接触控板")
            return@withContext false
        }

        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus){
            logTagV(TAG, "StudioSPlus版本, 不需要引导连接触控板")
            return@withContext false
        }

        val bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        // 已经连接过触控板,则不需要引导
        !bluetoothAdapter.bondedDevices.has {
            it.name in TOUCH_PAD_NAMES
        }
    }



    fun bootStartUpActivity() {
        val intent = Intent(this, StartUpActivity::class.java)
        startActivity(intent)
        launch {
            delay(ONE_SECOND)
            finish()    // 这里先让页面启动, 在销毁自己, 要不可能会黑一下
        }
    }

    /**
     * 根据归属地设置语言
     */
    private suspend fun setDefLanguage() = withContext(Dispatchers.IO) {
        when (Constants.starryHWInfo.salesLocale) {
            StarryDevLocale.Mainland -> {
                // 设置默认语言为汉语
                logTagV(TAG, "国内版, 简体中文")
                updateLanguage(hansCZ)
            }

            StarryDevLocale.Overseas -> {
                // 设置默认语言为英语
                logTagV(TAG, "海外版, 英文")
                updateLanguage(eng)
            }
        }

    }

    private suspend fun playSound() {
        val mediaPlay = MediaPlayer.create(this, R.raw.welcome_sound)
        mediaPlay.setOnCompletionListener {
            it.release()
        }
        mediaPlay.isLooping = false
        setBooleanSystemProp(KEY_SHOW_BOOT_IMG, true)
        delay(5 * ONE_SECOND)
        mediaPlay.start()
    }

    /**
     * 设置默认时区
     */
    private suspend fun setDefTimeZone(): Unit = withContext(Dispatchers.IO) {
        val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val timeZoneId = "Asia/Shanghai"
        logTagD(TAG, "设置时区:${timeZoneId}")
        alarmManager.setTimeZone(timeZoneId)
    }

    private suspend fun setVolumeToDef() = withContext(Dispatchers.IO) {
        setVolume(AudioManager.STREAM_VOICE_CALL, VOLUME_CALL_DEF)
        setVolume(AudioManager.STREAM_MUSIC, VOLUME_MEDIA_DEF)
    }

    private fun setVolume(streamType: Int, volume: Int) {
        val maxVolume = audioManager.getStreamMaxVolume(streamType)
        val targetVolume = round(maxVolume.toFloat() / SHOW_MAX_VOLUME * volume).toInt()

        logTagV(
            TAG,
            "设置音量:streamType:${streamType}, 目标音量:${volume}(${targetVolume}), 最大音量:${maxVolume}"
        )
        audioManager.setStreamVolume(streamType, targetVolume, AudioManager.FLAG_ALLOW_RINGER_MODES)
    }

    private suspend fun initScreenShare() = withContext(Dispatchers.IO) {
        logTagD(TAG, "无线投屏")
        val eShareServerSDK = EShareServerSDK.getSingleton(this@WelcomeActivity)
        // 5. 关闭EShare
        eShareServerSDK.stopEShareServer()
    }

    private fun bootCZKeyStone() {
        logTagV(TAG, "启动自动对焦服务")
        val intent = Intent().apply {
            `package` = "com.czur.keystone"
            action = "com.czur.keystone.ACTION.START"
        }
        startService(intent)
    }
}