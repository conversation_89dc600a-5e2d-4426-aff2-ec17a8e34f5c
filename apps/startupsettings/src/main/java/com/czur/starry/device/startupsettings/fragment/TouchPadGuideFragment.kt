package com.czur.starry.device.startupsettings.fragment

import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.baselib.view.BaseFragment
import com.czur.starry.device.startupsettings.R
import com.czur.starry.device.startupsettings.StartUpFinishActivity
import com.czur.starry.device.startupsettings.widget.TouchSliderView

/**
 * Created by 陈丰尧 on 2022/9/5
 */
private const val TAG = "TouchPadGuideFragment"

class TouchPadGuideFragment : BaseFragment() {
    override fun getLayoutId(): Int = R.layout.fragment_touch_pad_guide

    private val touchGuidIv by findView<ImageView>(R.id.touchGuidIv)
    private val touchSliderView by findView<TouchSliderView>(R.id.touchSliderView)
    private val touchGuidHintTv by findView<TextView>(R.id.guideHintTv)

    override fun initView() {
        super.initView()

        when (Constants.starryHWInfo.salesLocale) {
            StarryDevLocale.Mainland -> {
                logTagD(TAG, "国内版本, 按照触控板一代引导")
                Glide.with(this).load(R.drawable.img_touch_guide_anim_v2).into(touchGuidIv)
                touchGuidHintTv.setText(R.string.str_touch_pad_guide_hint_v2)
            }

            StarryDevLocale.Overseas -> {
                logTagD(TAG, "海外版本, 按照触控板二代引导")
                Glide.with(this).load(R.drawable.img_touch_guide_anim_v2).into(touchGuidIv)
                touchGuidHintTv.setText(R.string.str_touch_pad_guide_hint_v2)
            }
        }


        touchSliderView.onSlideSuccListener = {
            (requireActivity() as StartUpFinishActivity).moveToFinish()
        }
    }
}