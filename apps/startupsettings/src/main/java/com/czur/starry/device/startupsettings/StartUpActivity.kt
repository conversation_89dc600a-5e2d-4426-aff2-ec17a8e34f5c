package com.czur.starry.device.startupsettings

import com.czur.czurutils.log.logTagW
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagD
import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import com.czur.starry.device.baselib.base.BaseStartupActivity.Companion.KEY_NEXT_STEP_DATA
import com.czur.starry.device.baselib.base.NoNavActivity
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*

/**
 * Created by 陈丰尧 on 2/23/21
 */

private const val TAG = "StartUpActivity"

private const val KEY_FLOW_TYPE = "flowType"
private const val FLOW_TYPE_NORMAL = "normal"
private const val FLOW_TYPE_START_UP = "startUp"

class StartUpActivity : NoNavActivity() {
    private val stepStack: Stack<Step> = Stack()
    private var flowType: String = ""
    private var isFirstLaunch = true


    override fun getLayout() = R.layout.activity_boot

    override fun onCreate(savedInstanceState: Bundle?) {
        overridePendingTransition(0, R.anim.anim_aty_none)
        super.onCreate(savedInstanceState)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        logTagW(TAG, "又一次启动StartUpActivity!!${intent}")
    }


    override fun handlePreIntent(preIntent: Intent) {
        super.handlePreIntent(preIntent)
        flowType = preIntent.getStringExtra(KEY_FLOW_TYPE) ?: FLOW_TYPE_START_UP
    }


    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus && isFirstLaunch) {
            isFirstLaunch = false
            launch {
                logTagV(TAG,"onWindowFocusChanged - moveToNextStep")
                moveToNextStep()
            }
        }
    }

    /**
     * 上一步
     */
    private suspend fun moveToPreviousStep() = withContext(Dispatchers.IO) {
        logTagD(TAG, "上一步")
        stepStack.pop()
        if (stepStack.isEmpty()) {
            stepStack.push(findStep(STEP_FIRST_FLOW))
        }
        val step = stepStack.peek()
        if (!step.inHistory()) {
            logTagD(TAG, "不显示在历史记录中")
            _moveToPreviousStep()
            return@withContext
        }
        try {
            logTagD(TAG, "启动:${step.requestCode}")
            val data = step.data ?: Bundle()
            startActivityForResult(
                step.getIntent().putExtra(KEY_NEXT_STEP_DATA, data),
                step.requestCode
            )
        } catch (e: Exception) {
            logTagD(TAG, "${step.requestCode} 启动失败,继续返回上一步:", tr = e)
            _moveToPreviousStep()
        }
    }

    private suspend fun _moveToPreviousStep() {
        moveToPreviousStep()
    }

    /**
     * 下一步
     */
    private suspend fun moveToNextStep(skipStep: Boolean = false, data: Intent? = null) =
        withContext(Dispatchers.IO) {
            logTagD(TAG, "moveToNextStep->")
            val step = if (stepStack.empty()) {
                val step = intent.getIntExtra("step", STEP_LANGUAGE)
                logTagD(TAG, "第一步:$step")
                findStep(step)
            } else {
                val currentStep = stepStack.peek()
                var nextCode = if (skipStep) currentStep.skipCode else currentStep.nextCode
                if (currentStep.requestCode == STEP_WIFI && currentStep.nextCode == STEP_BEGIN && Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas) {
                    nextCode = STEP_FINISH_FLOW
                } else {
                    if (nextCode == STEP_FLOW) {
                        logTagD(TAG, "下一步是Flow")
                        // 代表需要从data中获取下一个flow 的id
                        data?.let {
                            val nexFlowName = data.getIntExtra(RESULT_FLOW, STEP_FINISH_FLOW)
                            // 根据flowID 找到下一步Code
                            nextCode = currentStep.resultFlows()[nexFlowName] ?: STEP_FINISH_FLOW
                        } ?: run {
                            nextCode = STEP_FINISH_FLOW
                        }
                    }
                    logTagD(TAG, "下一步ID:${nextCode}")
                }

                if (checkOrExit(nextCode)) {
                    return@withContext
                }
                findStep(nextCode)
            }

            val intent = step.getIntent()
            try {
                // 从上一步来的数据,传递给下一步
                val bundle = data?.getBundleExtra(KEY_NEXT_STEP_DATA) ?: Bundle()
                step.data = bundle
                stepStack.push(step)
                startActivityForResult(
                    intent.putExtra(KEY_NEXT_STEP_DATA, bundle),
                    step.requestCode
                )
            } catch (e: Exception) {
                logTagD(TAG, "${step.requestCode} 启动失败,继续下一步:", tr = e)
                launch {
                    onNextStepError(data)
                }
            }
        }

    private suspend fun onNextStepError(data: Intent?) {
        moveToNextStep(data = data)
    }

    private fun checkOrExit(stepCode: Int): Boolean {
        if (stepCode == STEP_FINISH_FLOW) {
            if (flowType != FLOW_TYPE_NORMAL) {
                // 初期设定Flow, 启动结束页面
                startFinishPage()
            }
            finish()
            return true
        }
        return false
    }

    private fun startFinishPage() {
        val intent = Intent(this, StartUpFinishActivity::class.java)
        startActivity(intent)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        logTagD(TAG, "设定完成:${requestCode}, 结果:$resultCode")
        // 通常的处理方式
        processNormalStep(resultCode, data)
    }

    private fun processNormalStep(resultCode: Int, data: Intent?) {
        launch {
            when (resultCode) {
                RESULT_OK -> moveToNextStep(data = data)
                RESULT_CANCELED -> moveToNextStep(true, data)
                RESULT_PRE -> moveToPreviousStep()
            }
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        // 拦截所有事件
        return true
    }
}