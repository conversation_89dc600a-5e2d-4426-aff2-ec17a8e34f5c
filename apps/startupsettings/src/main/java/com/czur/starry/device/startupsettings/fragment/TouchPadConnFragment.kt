package com.czur.starry.device.startupsettings.fragment

import android.graphics.drawable.Drawable
import android.text.Spannable
import android.text.SpannableString
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.baselib.view.BaseFragment
import com.czur.starry.device.baselib.view.CenteredImageSpan
import com.czur.starry.device.startupsettings.R
import com.czur.starry.device.startupsettings.WelcomeActivity
import com.czur.uilib.btn.CZButton

/**
 * Created by 陈丰尧 on 2022/9/13
 */
class TouchPadConnFragment : BaseFragment() {
    override fun getLayoutId(): Int = R.layout.fragment_touch_pad_conn

    private val touchPadIv by findView<ImageView>(R.id.touchPadIv)
    private val nextStepBtn by findView<CZButton>(R.id.nextStepBtn)
    private val pairTouchTv by findView<TextView>(R.id.pairTouchTv)
    private val expand = 3

    override fun initView() {
        super.initView()
        // 这里没有用语言来切换图片, 而是使用版本来切换图片
        nextStepBtn.setOnDebounceClickListener {
            (requireActivity() as WelcomeActivity).bootStartUpActivity()
        }


        val iconId = R.drawable.ic_touch_pad_switch_key_white

        val imgDrawable =
            ContextCompat.getDrawable(requireContext(), iconId)?.apply {
                setBounds(0, 0, minimumWidth + expand, minimumHeight + expand)
            }

        val pairText = getString(R.string.connect_touch_pad_hint)
        setForceImage(imgDrawable!!, pairTouchTv, pairText)
    }

    private fun setForceImage(imgDrawable: Drawable, tv: TextView, text: String) {
        val placeholderCharacter = "[TouchControl]"
        val index = text.indexOf(placeholderCharacter)
        val span = SpannableString(text)

        val imgSpan = CenteredImageSpan(imgDrawable)
        span.setSpan(
            imgSpan,
            index,
            index + placeholderCharacter.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )

        tv.text = span
    }
}