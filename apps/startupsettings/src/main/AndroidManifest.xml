<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system"
    tools:ignore="ProtectedPermissions">

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SET_TIME_ZONE" />
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
    <uses-permission android:name="android.permission.MANAGE_USB" />

    <application
        android:name=".StartUpApp"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/BaseAppTheme">

        <meta-data
            android:name="design_width_in_dp"
            android:value="1920" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="1080" />

        <activity
            android:name=".WelcomeActivity"
            android:configChanges="${atyPlaceHolder}"
            android:excludeFromRecents="true"
            android:theme="@style/ActivityWelcome"
            android:exported="true"/>

        <activity
            android:name=".StartUpActivity"
            android:clearTaskOnLaunch="true"
            android:configChanges="${startUpatyPlaceHolder}"
            android:excludeFromRecents="true"
            android:exported="true" />

        <activity-alias
            android:name=".BootActivity"
            android:clearTaskOnLaunch="true"
            android:configChanges="${atyPlaceHolder}"
            android:enabled="true"
            android:excludeFromRecents="true"
            android:exported="true"
            android:targetActivity=".WelcomeActivity">
            <intent-filter android:priority="3">
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.HOME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity-alias>

        <activity
            android:name=".StartUpFinishActivity"
            android:autoRemoveFromRecents="true"
            android:configChanges="${atyPlaceHolder}"
            android:excludeFromRecents="true" />


        <service android:name=".StartUpFinishSettingWindow" />

    </application>

</manifest>