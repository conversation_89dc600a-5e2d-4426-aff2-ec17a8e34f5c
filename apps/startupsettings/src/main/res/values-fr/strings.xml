<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Paramètres de démarrage</string>
    <string name="welcome">Bienvenue</string>
    <string name="str_startup_finish_hint">Configuration du système en cours. N’éteignez pas l’appareil.</string>
    <string name="title_touch_pad_guide">Faites glisser pour terminer le paramétrage</string>
    <string name="str_touch_pad_guide_hint">Faites glisser la cible : double-cliquez puis faites glisser la cible avec le même doigt (ne levez pas le doigt après le double clic).</string>
    <string name="str_touch_pad_guide_hint_v2">Faites glisser la cible : touchez la cible puis glissez pour la déplacer (ne levez pas le doigt après avoir touché la cible).</string>
    <string name="str_touch_pad_guide_slide_hint">Faites glisser vers \nle côté droit.</string>
    <string name="title_connect_touch_pad">Connectez le panneau tactile.</string>
    <string name="connect_touch_pad_hint">1. Met<PERSON><PERSON> le bouton marche/arrêt sur ON (marche). \n2. Appui long (3 secondes) sur le bouton [TouchControl] du coin supérieur gauche. \n     Quand le voyant bleu clignote, remettez le panneau tactile sur la station de chargement. \n3. Attendez que l’écran affiche \ « Le panneau tactile est connecté\ ».</string>
    <string name="startup_next_step">Suivant</string>
    <string name="connect_touch_pad_hint_mainland">1. Tournez le bouton d\'alimentation de TouchBoard (à l\'arrière) sur ON. \n2. Appuyez longuement sur le bouton [TouchControl] au coin supérieur gauche pendant 5 secondes, lorsque le voyant bleu clignote, remettez TouchBoard en position de chargement.  \n3. Attendez qu\'une fenêtre contextuelle s\'affiche sur l\'écran de projection StarryHub indiquant « Couplage réussi ! ». </string>
</resources>
