<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">Impostazioni di avvio</string>
    <string name="welcome">Benvenuto</string>
    <string name="str_startup_finish_hint">Configurazione del sistema in corso, non spegnere.</string>
    <string name="title_touch_pad_guide">Trascina per completare l\'impostazione</string>
    <string name="str_touch_pad_guide_hint">Trascina l’obiettivo: Fai doppio clic, quindi fai scorrere il dito per trascinare l’obiettivo. (Non rilasciare il dito dopo il doppio clic)</string>
    <string name="str_touch_pad_guide_hint_v2">Trascina l’obiettivo: Fissa l’obiettivo, e poi scorri per trascinarlo/fissarlo (Non rilasciare dopo aver fissato l’obiettivo)</string>
    <string name="str_touch_pad_guide_slide_hint">Trascina sul \nlato destro.</string>
    <string name="title_connect_touch_pad">Collega la TouchBoard.</string>
    <string name="connect_touch_pad_hint">1. Porta il pulsante di alimentazione sullo stato ON. \n2. Premi a lungo il pulsante [TouchControl] nell\'angolo in alto a sinistra per 3 secondi; \n dopo il lampeggiamento della luce blu, rimetti la TouchBoard nel dock di ricarica. \n3. Attendi che sullo schermo venga visualizzato il messaggio \"La TouchBoard è collegata!\".</string>
    <string name="startup_next_step">Avanti</string>
    <string name="connect_touch_pad_hint_mainland">1. Portare il pulsante di alimentazione della TouchBoard (sul lato posteriore) sullo stato ON. \n2. Tenere premuto a lungo il pulsante [TouchControl] sull’angolo superiore sinistro per 5 secondi; quando la luce blu lampeggia, rimettere la TouchBoard in posizione di ricarica. \n3. Attendere che sullo schermo di proiezione di StarryHub venga visualizzata una finestra pop-up con la scritta \'Successfully paird!\' (Associazione riuscita!).</string>
</resources>
