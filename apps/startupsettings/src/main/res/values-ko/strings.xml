<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="app_name">시작 설정</string>
    <string name="welcome">환영합니다</string>
    <string name="str_startup_finish_hint">시스템 구성이 진행 중입니다, 전원을 끄지 마세요.</string>
    <string name="title_touch_pad_guide">드래그해 설정을 완료하세요</string>
    <string name="str_touch_pad_guide_hint">드래그 대상: 더블클릭한 다음 손가락을 밀어 대상을 드래그하세요. (더블클릭 후 손가락을 떼지 마세요.)</string>
    <string name="str_touch_pad_guide_hint_v2">드래그 대상: 대상을 탭한 다음 밀어서 드래그/이동합니다. (대상을 탭한 다음 손가락을 떼지 마세요.)</string>
    <string name="str_touch_pad_guide_slide_hint">오른쪽으로 \n드래그합니다.</string>
    <string name="title_connect_touch_pad">TeouchBoard를 연결하세요.</string>
    <string name="connect_touch_pad_hint">1. 전원 버튼을 켜서 ON 상태로 바꿉니다. \n2. 왼쪽 상단 코너의 [TouchControl] 커븥을 3초간 길게 누릅니다. \n     파란색 불이 깜박이면 TouchBoard를 다시 충전 도크에 가져다 놓습니다. \n3. 화면에 \"TouchBoard is connected!\"라고 표시될 때까지 기다립니다.</string>
    <string name="startup_next_step">다음</string>
    <string name="connect_touch_pad_hint_mainland">1. (뒷면의) TouchBoard 전원 버튼을 켜짐 상태로 바꿉니다. \n2. 왼쪽 상단 구석의 [TouchControl] 버튼을 5초간 길게 누르고, 파란색 표시등이 깜박일 때, TouchBoard를 다시 충전 위치에 놓습니다. \n3. \'성공적으로 페어링되었습니다!\'를 나타내는 StarryHub 프로젝션 화면이 팝업 창에 뜰 때까지 기다립니다.</string>
</resources>
