<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="1360px"
    android:layout_height="wrap_content"
    tools:ignore="PxUsage">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/transcriptionCL"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_grey_r_10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- android:layout_height="108px" -->
        <com.czur.starry.device.transcription.util.FixedLineHeightTextView
            android:id="@+id/leftResultTv"
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:layout_marginStart="40px"
            android:layout_marginTop="45px"
            android:layout_marginEnd="40px"
            android:gravity="start|top"
            android:includeFontPadding="false"
            android:maxLines="3"
            android:minHeight="36px"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintEnd_toStartOf="@+id/middleLine"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--                android:layout_height="72px"    -->
        <com.czur.starry.device.transcription.util.FixedLineHeightTextView
            android:id="@+id/leftTransResultTv"
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:layout_marginStart="40px"
            android:layout_marginEnd="40px"
            android:gravity="start|top"
            android:includeFontPadding="false"
            android:minHeight="36px"
            android:textColor="#ffFFDB10"
            android:textSize="16sp"
            app:layout_constraintEnd_toStartOf="@+id/middleLine"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/leftResultTv" />

        <FrameLayout
            android:id="@+id/middleLine"
            android:layout_width="1px"
            android:layout_height="0px"
            android:layout_marginTop="44px"
            android:layout_marginBottom="44px"
            android:background="#7FFFFFFF"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.czur.starry.device.transcription.util.FixedLineHeightTextView
            android:id="@+id/rightResultTv"
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:layout_marginStart="40px"
            android:layout_marginTop="45px"
            android:layout_marginEnd="40px"
            android:gravity="start|top"
            android:includeFontPadding="false"
            android:maxLines="3"
            android:minHeight="36px"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/middleLine"
            app:layout_constraintTop_toTopOf="parent" />

        <!--                android:layout_height="72px"    -->
        <com.czur.starry.device.transcription.util.FixedLineHeightTextView
            android:id="@+id/rightTransResultTv"
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:layout_marginStart="40px"
            android:layout_marginEnd="40px"
            android:gravity="start|top"
            android:includeFontPadding="false"
            android:minHeight="36px"
            android:textColor="#ffFFDB10"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/middleLine"
            app:layout_constraintTop_toBottomOf="@+id/rightResultTv" />


        <com.czur.starry.device.transcription.widget.AnimatedImageCloudsView
            android:id="@+id/cloudsView"
            android:layout_width="100px"
            android:layout_height="33px"
            android:layout_marginBottom="3px"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="@id/scaleWindowIv"
            app:layout_constraintEnd_toStartOf="@id/duringTimeTv"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/duringTimeTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="7px"
            android:text="00:00:00"
            android:textColor="@color/color_transparent_white_80"
            android:textSize="20px"
            app:layout_constraintBottom_toBottomOf="@+id/scaleWindowIv"
            app:layout_constraintRight_toLeftOf="@+id/stopTransIv"
            app:layout_constraintTop_toTopOf="@+id/scaleWindowIv" />

        <ImageView
            android:id="@+id/stopTransIv"
            android:layout_width="56px"
            android:layout_height="56px"
            android:padding="13px"
            android:scaleType="fitXY"
            android:src="@drawable/ic_stop_trans"
            app:layout_constraintEnd_toStartOf="@+id/scaleWindowIv"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/scaleWindowIv"
            android:layout_width="56px"
            android:layout_height="56px"
            android:padding="13px"
            android:src="@drawable/ic_langs_scale"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/leftNetErrorTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/str_translation_network_poor"
            android:textColor="@color/notice_read"
            android:textSize="24px"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--        <androidx.constraintlayout.widget.Group-->
        <!--            android:id="@+id/talkModeGroup"-->
        <!--            android:visibility="gone"-->
        <!--            android:layout_width="wrap_content"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            app:constraint_referenced_ids="rightResultTv,middleLine,rightTransResultTv" />-->
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/smallCL"
        android:layout_width="176px"
        android:layout_height="66px"
        android:background="@drawable/shape_grey_radio_33"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/smallClickCL"
            android:layout_width="176px"
            android:layout_height="66px"
            android:background="@drawable/shape_grey_radio_33"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/smallIv"
                android:layout_width="50px"
                android:layout_height="50px"
                android:layout_marginStart="8px"
                android:background="@drawable/shape_red_radio_25"
                android:padding="10px"
                android:src="@drawable/ic_small_trans_icon"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/smallTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8px"
                android:text="AI互译字幕"
                android:textColor="@color/white"
                android:textSize="20px"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/smallIv"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>