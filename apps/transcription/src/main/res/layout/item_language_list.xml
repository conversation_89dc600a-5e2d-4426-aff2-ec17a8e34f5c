<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/selectTargetLanguageCl"
    android:layout_width="match_parent"
    android:layout_height="50px"
    app:layout_constraintBottom_toBottomOf="@+id/translationComparison"
    app:layout_constraintStart_toEndOf="@+id/translationComparison"
    app:layout_constraintTop_toTopOf="@+id/translationComparison"
    tools:ignore="PxUsage">

    <!-- CNEN -->
    <TextView
        android:id="@+id/selectTargetLanguageTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24px"
        tools:text="CN"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <ImageView
        android:id="@+id/selectLanguageArrowRightIv"
        android:layout_width="32px"
        android:layout_height="32px"
        android:layout_marginEnd="22px"
        android:src="@drawable/ic_select_language"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
