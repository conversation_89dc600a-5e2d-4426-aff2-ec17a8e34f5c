<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:sharedUserId="android.uid.system"
    tools:ignore="ProtectedPermissions">

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.SYSTEM_CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />


    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" />
    <uses-permission android:name="android.permission.CAPTURE_SECURE_VIDEO_OUTPUT" />
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.MANAGE_USB" />

    <application
        android:name=".App"
        android:icon="@mipmap/icon"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">

        <activity
            android:name=".activity.MainActivity"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:exported="true"
            android:launchMode="singleTop">
            <intent-filter>
                                <action android:name="com.czur.starry.device.transcription.BOOT_APP" />
                                <category android:name="android.intent.category.DEFAULT" />
<!--                <action android:name="android.intent.action.MAIN" />-->
<!--                <category android:name="android.intent.category.LAUNCHER" />-->
            </intent-filter>
        </activity>

        <service android:name=".service.SubtitleTransAlertWindowService" />

        <!--    只做重命名操作, 从主进程中独立出来,防止在多任务时影响主进程    -->
        <activity
            android:name=".activity.TransSettingWindowActivity"
            android:configChanges="${atyPlaceHolder}"
            android:theme="@style/DialogActivity"
            android:launchMode="singleTask"
            android:windowSoftInputMode="stateHidden|adjustResize" />
        <activity
            android:name=".activity.TransGetMemberTimeWindowActivity"
            android:configChanges="${atyPlaceHolder}"
            android:theme="@style/DialogActivity"
            android:windowSoftInputMode="stateHidden|adjustResize" />

        <provider
            android:name=".provider.TranscriptionProvider"
            android:authorities="com.czur.starry.device.transcription.provider.TranscriptionProvider"
            android:exported="true"
            tools:ignore="ExportedContentProvider" />

        <activity
            android:name=".activity.AITransRenameDialogActivity"
            android:configChanges="${atyPlaceHolder}"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/DialogActivity"
            android:process=":aiTransRename"
            android:taskAffinity="transcription.rename"
            android:windowSoftInputMode="stateHidden|adjustResize" />

        <activity
            android:name=".dialog.AITransWarnTimeActivity"
            android:configChanges="${atyPlaceHolder}"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/DialogActivity"
            android:process=":aITransWarnTimeActivity"
            android:taskAffinity="transcription.aiwarntime"
            android:windowSoftInputMode="stateHidden|adjustResize" />
        <activity
            android:name=".dialog.AITransWarnContentActivity"
            android:configChanges="${atyPlaceHolder}"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:theme="@style/DialogActivity"
            android:process=":aITransWarnContentActivity"
            android:taskAffinity="transcription.aiwarncontent"
            android:windowSoftInputMode="stateHidden|adjustResize" />

    </application>

</manifest>