package com.czur.starry.device.transcription

import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND

/**
 * Created by 宋清君 on 2022/8/6
 */
object Config {

    /**
     * **********客户端给的错误码**********
     * 	enum StatusCode {
     *         CZSL_OK = 0,                         /* 成功执行函数 */
     *         CZSL_CONNECT_FAIL = -1,              /* 连接/重连时出错 */
     *         CZSL_SET_PARAM_FAIL = -2,            /* 客户端设置参数错误 */
     *         CZSL_LANG_NONE = -3,                 /* 语言列表为空 */
     *         CZSL_RUNTIME_ERR = -4,				/* 运行时错误 */
     *     };
     * 	**********服务端返回的错误码**********
     * 	//http请求阶段，对应到APP应该就是init的时候
     * 	status_code == 10000001 || status_code == 10000004 || status_code == 10000006
     * 	//收发阶段，对应到APP就是start之后，收到错误码直接就不能再用了
     * 	status_code == 30000001 || status_code == 30000002 || status_code == 30000003 || status_code == 30000004
     * 	|| status_code == 30000005 || status_code == 40000001 || status_code == 40000003 || status_code == 40000102
     * 	|| status_code == 40000103 || status_code == 40000104 || status_code == 50000001 || status_code == 50000003
     * 	//收发阶段，对应到APP就是start之后，收到错误码还可以继续用，可能是类似于警告之类的东西
     * 	status_code == 30000006 || status_code == 30000007
     *
     * 	// 初始化失败 -1是有可能服务器状态不问题456都是超量报错
     */
    val ERROR_INIT_FAILED_CODE_LIST = listOf<Int>(-1, 10000001, 10000004, 10000005, 10000006)
    val ERROR_USING_NO_TIME_LIST = listOf<Int>(
        10000006,
        30000001,
        30000002
    ) // 使用过程中失败,没有会员时长了返回-501，"member expired init failed"。
    val ERROR_FAILED_CODE_LIST = listOf<Int>(
        -1,
        10000006,
        30000001,
        30000002,
        30000003,
        30000004,
        30000005,
        40000001,
        40000003,
        40000102,
        40000103,
        40000104,
        50000001,
        50000003
    ) // 使用过程中失败

    const val NET_CALLBACK_ERROR_CODE = -9527 //接口报错

    const val PREFERENCE_NAME = "aiTranscription"
    const val SOURCE_LANG = "sourceLanguage"
    const val TARGET_LANG = "targetLanguage"
    const val SHOW_CONTENT = "showContent"
    const val TRANS_CHILD_CONTENT = "transChild"
    const val GENERATE_MEETING_MINUTES = "generateMeetingMinutes"

    const val SHOW_CONTENT_TEXT = "1" // 实时字幕
    const val SHOW_CONTENT_TRANS = "0" // 现场互译
    const val DEFAULT_SHOW_CONTENT = SHOW_CONTENT_TEXT // 显示内容默认值

    const val TRANS_CHILD_SPEECH = "1" // 演讲模式
    const val TRANS_CHILD_TALK = "0" // 对话模式
    const val DEFAULT_TRANS_CHILD = TRANS_CHILD_SPEECH // 显示内容默认值

    const val BIG_TRANS_WIDTH = 1800
    const val BIG_TRANS_HEIGHT = 242
    const val BIG_TRANS_HEIGHT_TEXT = 172
    const val BIG_TRANS_X = 60
    const val BIG_TRANS_Y = 828
    const val BIG_TRANS_TEXT_Y = 898

    const val DEFAULT_AI_ICON_X = 1516

    const val MAX_TRANS_TIME = 5 * ONE_HOUR // 最大会议时间
    const val TRANS_REMIND_LEAVE_TIME = 20 * ONE_MIN // 会议结束前提醒时间
    const val TRANS_REMIND_NO_CONTENT_WARN_TIME = 4 * ONE_MIN // 没有内容提示
    const val TRANS_REMIND_NO_CONTENT_CLOSE_TIME = 5 * ONE_MIN // 没有内容关闭
    const val TRANS_REMIND_NO_CONTENT_CLEAN_TIME = 10 * ONE_SECOND // 没有内容清空时间
}