package com.czur.starry.device.transcription.service


import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpHeader
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.network.core.MiaoHttpPath
import com.czur.starry.device.baselib.network.core.MiaoHttpPost
import com.czur.starry.device.transcription.model.AITransGiftTime
import com.czur.starry.device.transcription.model.AITransMemberInfo

interface AITransHttpServer {


    /**查看纪要列表
     */
    @MiaoHttpGet("/api/starry/minute")
    fun getTransferRecord(
        @MiaoHttpParam("accountNo") accountNo: String = UserHandler.accountNo.toString(),
        @MiaoHttpParam("page") page: Int = 1,
        @MiaoHttpParam("size") size: Int = 10,
        @MiaoHttpParam("sortAlgo") sortAlgo: String = "ASC",
        @MiaoHttpParam("sortField") sortField: String = "create_time",
        clazz: Class<String> = String::class.java,
    ): MiaoHttpEntity<String>

    // 批量删除记录
    @MiaoHttpPost("/api/starry/minute/batchDelete")
    @MiaoHttpHeader("Content-Type: application/json")
    fun postBatchDelete(
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>

    // 批量查看纪要状态
    @MiaoHttpPost("/api/starry/minute/batchQuery")
    @MiaoHttpHeader("Content-Type: application/json")
    fun postTransferRecord(
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>

    /**
     * 领取会员
     */
    @MiaoHttpGet("/api/starry/minute/gift")
    fun getGift(
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>


    /**查询会员状态
     */
    @MiaoHttpGet("/api/starry/minute/memberInfo")
    fun getMemberInfo(
        clazz: Class<AITransMemberInfo> = AITransMemberInfo::class.java
    ): MiaoHttpEntity<AITransMemberInfo>

    @MiaoHttpGet("/api/starry/product/gift")
    fun getAITransGiftTime(
        clazz: Class<AITransGiftTime> = AITransGiftTime::class.java
    ): MiaoHttpEntity<AITransGiftTime>


    // 更新纪要名称
    @MiaoHttpPost("/api/starry/minute/{id}")
    fun postRenameRecord(
        @MiaoHttpPath("id") id: String,
        @MiaoHttpParam("name") name: String,
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>

}