package com.czur.starry.device.transcription

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.hdmilib.HDMIMonitor
import com.czur.starry.device.transcription.Config.NET_CALLBACK_ERROR_CODE
import com.czur.starry.device.transcription.model.AITransMemberInfo
import com.czur.starry.device.transcription.service.AITransHttpServer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext

private const val TAG = "MainViewModel"

class MainViewModel(application: Application) : AndroidViewModel(application) {
    private val transferServer: AITransHttpServer by lazy { HttpManager.getService() }

    var memberInfoFlow =
        MutableStateFlow<AITransMemberInfo>(AITransMemberInfo(accountNo = "0", 0L, 0, 0, true))
    var memberInfo
        get() = memberInfoFlow.value
        set(value) {
            memberInfoFlow.value = value
        }

    var memberInformationHasBeenRefreshed = false // 是否刷新过刷新会员信息

    private val hdmiMonitor = HDMIMonitor()


    fun getHDMIStatus() = hdmiMonitor.getHDMIIFStatus()


    private suspend fun getAITransMemberInfo(): AITransMemberInfo {
        return withContext(Dispatchers.IO) {
            try {
                val infoDeferred = async(Dispatchers.IO) {
                    transferServer.getMemberInfo()
                }
                val giftTimeDeferred = async(Dispatchers.IO) {
                    transferServer.getAITransGiftTime()
                }
                val info = infoDeferred.await()
                val giftTime = giftTimeDeferred.await()
                info.withCheck().body.copy(
                    giftTime = giftTime.body.alias
                ).also {
                    memberInformationHasBeenRefreshed = true    // 只有成功过去了,才认为是被刷新过了
                }
                // 调用API或其他异步操作
            } catch (tr: Exception) {
                logTagW(TAG, "getAITransMemberInfo Error", tr = tr)
                // 发生错误时返回默认值
                AITransMemberInfo(accountNo = "0", 0L, 0, 0, true, NET_CALLBACK_ERROR_CODE)
            }
        }
    }

    // 刷新会员信息
    fun refreshMemberInfo() {
        if (UserHandler.isLogin) {
            launch {
                val info = getAITransMemberInfo()
                memberInfo = info
            }
        }
    }

    suspend fun getGift(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val gift = transferServer.getGift()
                if (gift.isSuccess) {
                    gift.withCheck()
                    true
                } else {
                    false
                }
            } catch (e: Exception) {
                false
            }
        }
    }


    fun formatDuration(milliseconds: Long): String {
        val totalSeconds = milliseconds / 1000
        val hours = totalSeconds / 3600
        val remainingSeconds = totalSeconds % 3600
        val minutes = remainingSeconds / 60
        val seconds = remainingSeconds % 60

        return when {
            hours > 0L && minutes > 0L && seconds > 0L -> "${hours}小时${minutes}分钟${seconds}秒"
            hours > 0L && minutes > 0L && seconds == 0L -> "${hours}小时${minutes}分钟"
            hours > 0L && minutes == 0L && seconds > 0L -> "${hours}小时0分钟${seconds}秒"
            hours > 0L && minutes == 0L && seconds == 0L -> "${hours}小时"
            hours == 0L && minutes > 0L && seconds > 0L -> "${minutes}分钟${seconds}秒"
            hours == 0L && minutes > 0L && seconds == 0L -> "${minutes}分钟"
            else -> "${seconds}秒"
        }
    }


}