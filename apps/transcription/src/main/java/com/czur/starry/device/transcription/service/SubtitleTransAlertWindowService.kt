package com.czur.starry.device.transcription.service

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.audioai.AudioAiServiceCallback
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import com.czur.czurutils.log.logStackTrace
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.common.KEY_HDMI_STATE_IN
import com.czur.starry.device.baselib.data.provider.TransHandler
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.tips.ServiceFloatTipUtil
import com.czur.starry.device.baselib.utils.CZPowerManager
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.getNumberDurationStr
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.transcription.Config.BIG_TRANS_HEIGHT
import com.czur.starry.device.transcription.Config.BIG_TRANS_HEIGHT_TEXT
import com.czur.starry.device.transcription.Config.BIG_TRANS_TEXT_Y
import com.czur.starry.device.transcription.Config.BIG_TRANS_WIDTH
import com.czur.starry.device.transcription.Config.BIG_TRANS_X
import com.czur.starry.device.transcription.Config.BIG_TRANS_Y
import com.czur.starry.device.transcription.Config.DEFAULT_AI_ICON_X
import com.czur.starry.device.transcription.Config.DEFAULT_SHOW_CONTENT
import com.czur.starry.device.transcription.Config.DEFAULT_TRANS_CHILD
import com.czur.starry.device.transcription.Config.ERROR_FAILED_CODE_LIST
import com.czur.starry.device.transcription.Config.ERROR_USING_NO_TIME_LIST
import com.czur.starry.device.transcription.Config.GENERATE_MEETING_MINUTES
import com.czur.starry.device.transcription.Config.MAX_TRANS_TIME
import com.czur.starry.device.transcription.Config.PREFERENCE_NAME
import com.czur.starry.device.transcription.Config.SHOW_CONTENT
import com.czur.starry.device.transcription.Config.SHOW_CONTENT_TEXT
import com.czur.starry.device.transcription.Config.SHOW_CONTENT_TRANS
import com.czur.starry.device.transcription.Config.SOURCE_LANG
import com.czur.starry.device.transcription.Config.TARGET_LANG
import com.czur.starry.device.transcription.Config.TRANS_CHILD_CONTENT
import com.czur.starry.device.transcription.Config.TRANS_CHILD_SPEECH
import com.czur.starry.device.transcription.Config.TRANS_CHILD_TALK
import com.czur.starry.device.transcription.Config.TRANS_REMIND_LEAVE_TIME
import com.czur.starry.device.transcription.Config.TRANS_REMIND_NO_CONTENT_CLEAN_TIME
import com.czur.starry.device.transcription.Config.TRANS_REMIND_NO_CONTENT_CLOSE_TIME
import com.czur.starry.device.transcription.Config.TRANS_REMIND_NO_CONTENT_WARN_TIME
import com.czur.starry.device.transcription.R
import com.czur.starry.device.transcription.activity.AITransRenameDialogActivity
import com.czur.starry.device.transcription.dialog.AITransWarnContentActivity
import com.czur.starry.device.transcription.dialog.AITransWarnTimeActivity
import com.czur.starry.device.transcription.manager.AudioAiManager
import com.czur.starry.device.transcription.util.FixedLineHeightTextView
import com.czur.starry.device.transcription.util.FrontEllipsizeHelper
import com.czur.starry.device.transcription.widget.AnimatedImageCloudsView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import kotlin.math.abs


private const val TAG = "SubtitleTransAlertWindowService"

class SubtitleTransAlertWindowService : AlertWindowService() {

    private val sharedPreferences by lazy {
        getSharedPreferences(PREFERENCE_NAME, MODE_PRIVATE)
    }

    private val sourceLanguage by lazy {
        sharedPreferences.getString(SOURCE_LANG, "CN")
    }

    private val targetLanguage by lazy {
        sharedPreferences.getString(TARGET_LANG, "EN")
    }

    private val showContent by lazy {
        sharedPreferences.getString(SHOW_CONTENT, DEFAULT_SHOW_CONTENT)
    }

    private val transChildContent by lazy {
        sharedPreferences.getString(TRANS_CHILD_CONTENT, DEFAULT_TRANS_CHILD)
    }

    private val generateMeetingMinutes by lazy {
        sharedPreferences.getBoolean(GENERATE_MEETING_MINUTES, true)
    }

    private lateinit var screenLockReceiver: BroadcastReceiver

    // 网络状态监听
    private val netStatusUtil: NetStatusUtil by lazy {
        NetStatusUtil(this)
    }

    private val wakeupScreenLock by lazy {
        CZPowerManager.createOneWakeLock("aiTransWakeUpScreen")
    }

    // 是否显示网络错误提示
    private var isShowingNetworkError = false

    // 最后一次内容回调时间
    private var lastContentCallbackTime = System.currentTimeMillis()

    override val layoutId: Int
        get() = R.layout.float_text_layout

    //    override val windowWidth: Int
//        get() = BIG_TRANS_WIDTH
    override val windowWidth: Int
        get() = BIG_TRANS_WIDTH
    override val windowHeight: Int
        get() = BIG_TRANS_HEIGHT
    override val xOffSet: Int
        get() = BIG_TRANS_X
    override val yOffset: Int
        get() = BIG_TRANS_Y

    override val careKeyEvent: Boolean = false
    override val draggable: Boolean
        get() = true
    override val autoAdsorption: Boolean
        get() = false

    private val transcriptionCL: ConstraintLayout by ViewFinder(R.id.transcriptionCL)
    private val scaleWindowIv: ImageView by ViewFinder(R.id.scaleWindowIv)
    private val leftResultTv: FixedLineHeightTextView by ViewFinder(R.id.leftResultTv)
    private val leftTransResultTv: FixedLineHeightTextView by ViewFinder(R.id.leftTransResultTv)
    private val rightResultTv: FixedLineHeightTextView by ViewFinder(R.id.rightResultTv)
    private val rightTransResultTv: FixedLineHeightTextView by ViewFinder(R.id.rightTransResultTv)
    private val leftNetErrorTv: TextView by ViewFinder(R.id.leftNetErrorTv)
    private val middleLine: FrameLayout by ViewFinder(R.id.middleLine)
    private val cloudsView: AnimatedImageCloudsView by ViewFinder(R.id.cloudsView)
    private val stopTransIv: ImageView by ViewFinder(R.id.stopTransIv)
    private val duringTimeTv: TextView by ViewFinder(R.id.duringTimeTv)


    private var lastX = BIG_TRANS_X
    private var lastY = BIG_TRANS_Y

    private var leftHistoryContent = ""
    private var leftHistoryTrans = ""
    private var rightHistoryContent = ""
    private var rightHistoryTrans = ""
    private var currentContent = ""
    private var currentTrans = ""

    private var leaveTime: Long = 0L // 剩余时长

    // 记录开始翻译的时间
    // 开始后计时,如果是次卡,计时5小时,结束前20分钟弹窗
    // 如果计时模式,时间还剩20分钟时弹窗提示
    private var startTranslationTime = System.currentTimeMillis()

    private var clearTextJob: Job? = null
    private var startCheckHdmiJob: Job? = null
    private var warn20MinWatchJob: Job? = null
    private var duringTimeWatchJob: Job? = null

    // 标记是否是用户主动隐藏（用于区分是否需要播放隐藏动画）
    private var isUserHiding = false
    private var isFirstIn = true

    private val leftResultContentTvHelper: FrontEllipsizeHelper by lazy {
        FrontEllipsizeHelper(leftResultTv, 3)
    }

    private val leftResultTransTvHelper: FrontEllipsizeHelper by lazy {
        FrontEllipsizeHelper(leftTransResultTv, 2)
    }

    private val rightResultContentTvHelper: FrontEllipsizeHelper by lazy {
        FrontEllipsizeHelper(rightResultTv, 3)
    }

    private val rightResultTransTvHelper: FrontEllipsizeHelper by lazy {
        FrontEllipsizeHelper(rightTransResultTv, 2)
    }

    private fun startDuringTimeWatchJob() {
        duringTimeWatchJob = launch(Dispatchers.IO) {
            while (true) {
                val lng = System.currentTimeMillis() - startTranslationTime
                // 把lng时间格式化成HH:mm:ss
                val timeStr = getNumberDurationStr(lng)
                withContext(Dispatchers.Main) {
                    duringTimeTv.text = timeStr
                }

                // 任何会议都可以开5小时,如果是计时,最后一次开始,也给5小时时间
                if (lng >= MAX_TRANS_TIME) {
                    launch(Dispatchers.Main) {
                        toast(R.string.toast_ai_trans_single_time_over)
                    }
                    stopTrans()
                    break
                }

                delay(300) // 每300ms秒检查一次
            }
        }
    }

    // 只要是hdmi插入的状态,就会影响mic收声,就需要停止掉AI字幕
    private fun startCheckHdmiJob() {
        startCheckHdmiJob = launch(Dispatchers.IO) {
            while (true) {
                val booleanSystemProp = getBooleanSystemProp(KEY_HDMI_STATE_IN, false)
                if (booleanSystemProp) {
                    stopTrans()
                }
                delay(3000) // 每2秒检查一次
            }
        }
    }

    /**
     * 持续观察会议时间,剩余20min时候弹窗提醒
     */
    private fun startWarn20MinWatchJob() {
        warn20MinWatchJob = launch(Dispatchers.IO) {
            while (true) {
                // leaveTime: 总的持续时间
                val remainingTime = leaveTime - (System.currentTimeMillis() - startTranslationTime)
                if (remainingTime <= TRANS_REMIND_LEAVE_TIME) {
                    // 总时间大于20分钟，才弹窗提醒
                    if (leaveTime > TRANS_REMIND_LEAVE_TIME) {
                        withContext(Dispatchers.Main) {
                            intentToWarnAty()
                        }
                    }
                    break
                }
                delay(getCheckDelayTime(remainingTime))
            }
        }
    }

    private fun getCheckDelayTime(remainingTime: Long): Long {
        return if (remainingTime > 10 * ONE_MIN) {
            ONE_MIN
        } else if (remainingTime > 5 * ONE_MIN) {
            30 * ONE_SECOND
        } else if (remainingTime > 30 * ONE_SECOND) {
            ONE_SECOND
        } else {
            200L
        }
    }

    private fun startNoContentCallbackWatchJob() {
        clearTextJob = launch(Dispatchers.IO) {
            while (true) {
                if ((System.currentTimeMillis() - lastContentCallbackTime) > (TRANS_REMIND_NO_CONTENT_WARN_TIME)) {
                    logTagI(TAG, "超过4分钟没有内容回调，弹窗提示")
                    showNoContentDialog()
                    break
                } else
                // 检查是否超过5分钟没有内容回调
                    if ((System.currentTimeMillis() - lastContentCallbackTime) > (TRANS_REMIND_NO_CONTENT_CLOSE_TIME)) {
                        logTagI(TAG, "超过5分钟没有内容回调，自动停止")
                        stopTrans()
                        break
                    } else if ((System.currentTimeMillis() - lastContentCallbackTime) > (TRANS_REMIND_NO_CONTENT_CLEAN_TIME)) {
                        // 10秒没有新内容，清空文本并重置行数计数
                        makeDataText(isEndOfASentence = false, content = "", clearText = true)
                    }
                delay(2000) // 每2秒检查一次
            }
        }
    }

    private fun showNoContentDialog() {
        TransHandler.hasContent = false
        val intent = Intent(this, AITransWarnContentActivity::class.java).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        startActivity(intent)
    }

    // 内部回调类
    private val mAudioAiServiceCallback: AudioAiServiceCallback = object : AudioAiServiceCallback {


        override fun onAudioAsrResult(result: String, roleres: String) {
            makeDataText(isEndOfASentence = false, content = result, roleres = roleres)
        }

        override fun onAudioTranslateResult(result: String, roleres: String) {
            makeDataText(isEndOfASentence = true, content = result, roleres = roleres)
        }

        override fun onAudioAiError(errorCode: Int, errorMsg: String) {
            logTagI(TAG, "onAudioAiError errorCode:$errorCode, errorMsg:$errorMsg")
            when (errorCode) {
                -500, -501, -502 -> {
                    //   java层返回的,不处理,直接和算法服务层的错误码对接
                }

                in ERROR_USING_NO_TIME_LIST -> {//时长不足
                    launch(Dispatchers.IO) {
                        delay(500)
                        withContext(Dispatchers.Main) {
                            NoticeHandler.sendMessage(
                                MsgType(
                                    MsgType.COMMON,
                                    MsgType.COMMON_TOAST
                                )
                            ) {
                                put(getString(R.string.toast_ai_has_no_member_time))
                            }
                            stopTrans()

                        }
                    }
                    logTagI(TAG, "onAudioAiError 会员时长不足")
                }

                in ERROR_FAILED_CODE_LIST -> {//过程中的其他错误
                    launch(Dispatchers.IO) {
                        delay(500)
                        withContext(Dispatchers.Main) {
                            NoticeHandler.sendMessage(
                                MsgType(
                                    MsgType.COMMON,
                                    MsgType.COMMON_TOAST
                                )
                            ) {
                                put(getString(R.string.toast_ai_error_in_using))
                            }
                            stopTrans()
                        }
                    }
                    logTagI(TAG, "onAudioAiError 过程中的其他错误")
                }

                else -> {

                }
            }
        }

        override fun onAsrMicAmp(value: String?) {
            cloudsView.setAmpValue(if (value.isNullOrEmpty()) "" else value)
        }
    }

    private fun intentToWarnAty() {
        val intent = Intent(this, AITransWarnTimeActivity::class.java).apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        startActivity(intent)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)

        val fileName = intent?.getStringExtra("fileName")
        leaveTime = intent?.getLongExtra("leaveTime", 0L) ?: 0L
        logTagI(
            TAG,
            "SubtitleTransAlertWindowService--onStartCommand fileName--$fileName leaveTime--$leaveTime"
        )

        onScreenOffListener = {
            stopTrans()
        }

        if (showContent == SHOW_CONTENT_TEXT ||
            (showContent != SHOW_CONTENT_TEXT && transChildContent == TRANS_CHILD_SPEECH)
        ) {
            // 实时字幕 或 现场互译-演讲模式
            lastY = if (showContent == SHOW_CONTENT_TEXT) BIG_TRANS_TEXT_Y else lastY
            middleLine.gone()
            ConstraintSet().apply {
                clone(transcriptionCL)
                clear(middleLine.id, ConstraintSet.START) // 取消 start 约束
                applyTo(transcriptionCL)
            }
        } else {
            // 其他情况（如对话模式）
        }
        // 登录状态监听
        UserHandler.isLoginLive.observe(this) {
            // 停止翻译,并刷新UI
            if (!it) {
                launch(Dispatchers.Main) {
                    toast(R.string.toast_ai_rename_success)
                }
                stopTrans(false)
            }
        }

        if (AudioAiManager.getServiceManager() == null) {
            AudioAiManager.init()
        }

        // 使用定义好的观察者变量
        TransHandler.stopTransLive.observe(this@SubtitleTransAlertWindowService) {
            if (it) {
                TransHandler.stopTrans = false
                stopTrans()
            }
        }

        TransHandler.showSubtitlesLive.observe(this@SubtitleTransAlertWindowService) {
            logTagI(TAG, "TransHandler.showSubtitlesLive observe $it")
            changeVis(it)
        }

        // 监听抖动动画触发
        TransHandler.triggerShakeAnimLive.observe(this@SubtitleTransAlertWindowService) {
            if (it) {
                logTagI(TAG, "收到抖动动画触发信号")
                // 立即重置状态，防止重复触发
                TransHandler.triggerShakeAnim = false
                if (TransHandler.showSubtitles && transcriptionCL.width > 1) {
                    // 只有在悬浮窗真正显示时才执行抖动动画
                    playShakeAnimation()
                } else {
                    logTagI(TAG, "悬浮窗未显示，跳过抖动动画")
                }
            }
        }

        logTagI(TAG, "SubtitleTransAlertWindowService--getSystemService")
        AudioAiManager.registerCallback(mAudioAiServiceCallback)
        logTagI(TAG, "mAudioAiServiceManager--${AudioAiManager.getServiceManager()}")
        AudioAiManager.getAsrLangs()?.forEach {
            logTagI(TAG, "getAsrLangs--$it")
        }
        AudioAiManager.getTranslateOrigLangs()?.forEach {
            logTagI(TAG, "translateOrigLangs--$it")
        }
        AudioAiManager.getTranslateTargetLangs()?.forEach {
            logTagI(TAG, "translateTargetLangs--$it")
        }


        AudioAiManager.setTranslateLang(
            sourceLanguage!!, targetLanguage!!
        )
        //比如需要识别多个，那asr语言就传CN==EN==JP==XX \
        // 翻译的话是两个语言${sourceLanguage}==${targetLanguage}
        AudioAiManager.setAsrLang("${sourceLanguage}==${targetLanguage}")
        logTagI(
            TAG, "showContent--${showContent}  " +
                    "generateMeetingMinutes--$generateMeetingMinutes   " +
                    "sourceLanguage--$sourceLanguage   " +
                    "targetLanguage--$targetLanguage"
        )

        when (showContent) {
            SHOW_CONTENT_TEXT -> {// 实时字幕
                AudioAiManager.setTranslateEnabled(false)
            }

            SHOW_CONTENT_TRANS -> {//现场互译
                AudioAiManager.setTranslateEnabled(true)
            }
        }

        val offsetMillis = TimeZone.getDefault().rawOffset
        val offsetHours = offsetMillis / (1000 * 60 * 60)
        val time = (if (offsetHours >= 0) "+" else "") + offsetHours
        logTagI("时区偏移（小时）: UTC$time")

        AudioAiManager.setSummaryEnabled(generateMeetingMinutes)
        AudioAiManager.setAsrName(fileName ?: "")
        AudioAiManager.setTimezone(time)
        AudioAiManager.startAsr()

        // 记录开始翻译的时间
        startTranslationTime = System.currentTimeMillis()
        lastContentCallbackTime = System.currentTimeMillis() // 初始化最后内容回调时间
        logTagI(TAG, "开始翻译，记录时间: $startTranslationTime")

        TransHandler.showSubtitles = true
        // 开始后显示计时时间
        startDuringTimeWatchJob()
        // 启动检查剩余20分钟任务
        startWarn20MinWatchJob()
        return START_NOT_STICKY // 不需要被kill后重建Service
    }

    override fun View.initViews() {

        cloudsView.initAnimation(true)
        // 设置固定行高，避免中英文切换时行高不一致导致的跳动问题
        leftResultTv.apply {
            // 设置固定行高（像素值）
            setLineHeightPX(36) // 每行36px的高度
            includeFontPadding = false // 禁用字体内边距
        }

        leftTransResultTv.apply {
            // 设置固定行高（像素值）
            setLineHeightPX(36) // 与resultTv使用相同的行高
            includeFontPadding = false
        }

        rightResultTv.apply {
            // 设置固定行高（像素值）
            setLineHeightPX(36) // 每行36px的高度
            includeFontPadding = false // 禁用字体内边距
        }

        rightTransResultTv.apply {
            // 设置固定行高（像素值）
            setLineHeightPX(36) // 与resultTv使用相同的行高
            includeFontPadding = false
        }

        scaleWindowIv.setOnDebounceClickListener { // 缩小到小窗
            // 标记为用户主动隐藏，需要播放动画
            isUserHiding = true
            playHideAnimation {
                TransHandler.showSubtitles = false
                isUserHiding = false
            }
        }

        stopTransIv.setOnDebounceClickListener {
            TransHandler.stopTrans = true
        }
    }

    override suspend fun initTask() {
        super.initTask()

    }

    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    override fun initData() {
        super.initData()

        // 在initData中设置tips，此时rootView已经创建完成
        rootView?.let { root ->
            // 自动为所有带有tips标记的view设置tips
            ServiceFloatTipUtil.setupFloatTipsForService(root, this)
        }


        // 初始化 ScreenLockReceiver
        screenLockReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    Intent.ACTION_SCREEN_OFF -> {
                        TransHandler.stopTrans = true
                    }
                }
            }
        }
        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_SCREEN_ON)
        }
        registerReceiver(screenLockReceiver, filter)

        // 启动网络状态监听
        netStatusUtil.startWatching()

        // 监听网络状态变化
        netStatusUtil.internetStatusLive.observe(this) { status ->
            logTagI(TAG, "网络状态变化: $status")
            if (status == InternetStatus.DISCONNECT) {
                // 网络断开，显示错误提示
                if (!isShowingNetworkError) {
                    makeDataText(
                        isEndOfASentence = false,
                        content = "",
                        clearText = false,
                        isNetworkError = true
                    )
                }
            } else if (status == InternetStatus.CONNECT && isShowingNetworkError) {
                // 网络恢复，清除错误提示
                isShowingNetworkError = false
                makeDataText(isEndOfASentence = false, content = "", clearText = true)
            }
        }
        // 启动hdmi是否存在的检查
        startCheckHdmiJob()

        // 启动无内容回调自动停止任务
        startNoContentCallbackWatchJob()

        wakeupScreenLock.acquire(5 * ONE_HOUR)  // AI字幕最多持续时长

        setFloatTip(scaleWindowIv, getString(R.string.tips_aicc_scale_to_small))
        setFloatTip(stopTransIv, getString(R.string.tips_aicc_click_stop))
    }

    /**
     * 手动为指定View设置悬浮提示
     */
    private fun setFloatTip(view: View, tipText: String, theme: Int = 0) {
        ServiceFloatTipUtil.setFloatTip(view, tipText, theme)
    }

    override fun onDestroy() {
        ServiceFloatTipUtil.clearAllFloatTips()

        cloudsView.stopAnimation()
        wakeupScreenLock.release()
        clearTextJob?.cancel()
        startCheckHdmiJob?.cancel()
        warn20MinWatchJob?.cancel()

        unregisterReceiver(screenLockReceiver)
        TransHandler.isTranslating = false

        // 停止网络状态监听
        netStatusUtil.stopWatching()
        launch {
            changeNetStatusUI(true)
        }
        super.onDestroy()
    }
    fun makeDataText(
        isEndOfASentence: Boolean,
        content: String,
        clearText: Boolean = false,
        isNetworkError: Boolean = false,
        roleres: String? = sourceLanguage
    ) {
        logTagI(
            TAG,
            " makeDataText-content--$content isEndOfASentence--$isEndOfASentence clearText--$clearText isNetworkError--$isNetworkError" +
                    " roleres--$roleres"
        )
        // 如果不是清空文本或网络错误，更新最后内容回调时间
        if (!clearText && !isNetworkError && content.isNotEmpty()) {
            lastContentCallbackTime = System.currentTimeMillis()
            TransHandler.hasContent = true
        }
        val textLiveIn: String? = if (showContent == SHOW_CONTENT_TEXT ||
            (showContent != SHOW_CONTENT_TEXT && transChildContent == TRANS_CHILD_SPEECH)
        ) {
            // 实时字幕 或 现场互译-演讲模式, 不关注rollers 都显示在sourceLanguage中
            sourceLanguage
        } else {
            roleres
        } // 当前文本应该显示在哪里
        launch(Dispatchers.IO) {
            currentContent = ""
            currentTrans = ""
            if (isNetworkError) {
                isShowingNetworkError = true
            }

            if (isShowingNetworkError) {
                // 显示网络错误提示
                withContext(Dispatchers.Main) {
                    changeNetStatusUI(false)

                    // 重置行数计数，清除补全记录
                    leftResultContentTvHelper.resetLineCount()
                    leftResultTransTvHelper.resetLineCount()
                    rightResultContentTvHelper.resetLineCount()
                    rightResultTransTvHelper.resetLineCount()

                    leftResultContentTvHelper.setText("", "", false)
                    leftResultTransTvHelper.setText("", "", false)
                    leftHistoryContent = ""
                    leftHistoryTrans = ""
                    rightResultContentTvHelper.setText("", "", false)
                    rightResultTransTvHelper.setText("", "", false)
                    rightHistoryContent = ""
                    rightHistoryTrans = ""
                }
                return@launch
            }

            changeNetStatusUI(true)


            val parts = content.split("=====", limit = 2)
            val currentContent = parts.getOrElse(0) { "" }
            var currentTrans = parts.getOrElse(1) { "" }

            if (showContent == SHOW_CONTENT_TEXT) {
                currentTrans = ""
                leftHistoryTrans = ""
            }

            withContext(Dispatchers.Main) {
                when {
                    !TransHandler.showSubtitles || clearText -> {
                        // 清空文本时重置行数计数，清除补全记录
                        leftResultContentTvHelper.resetLineCount()
                        leftResultTransTvHelper.resetLineCount()
                        rightResultContentTvHelper.resetLineCount()
                        rightResultTransTvHelper.resetLineCount()
                        // 直接设置空文本，避免使用setText方法可能添加的空白行
                        leftResultTv.text = ""
                        leftTransResultTv.text = ""
                        leftHistoryContent = ""
                        leftHistoryTrans = ""
                        rightResultTv.text = ""
                        rightTransResultTv.text = ""
                        rightHistoryContent = ""
                        rightHistoryTrans = ""
                    }

                    isEndOfASentence -> {

                        when (textLiveIn) {
                            sourceLanguage -> {
                                leftHistoryContent =
                                    leftResultContentTvHelper.setText(
                                        leftHistoryContent,
                                        currentContent,
                                        true
                                    )
                                leftHistoryTrans =
                                    leftResultTransTvHelper.setText(
                                        leftHistoryTrans,
                                        currentTrans,
                                        true
                                    )
                            }

                            targetLanguage -> {
                                rightHistoryContent =
                                    rightResultContentTvHelper.setText(
                                        rightHistoryContent,
                                        currentContent,
                                        true
                                    )
                                rightHistoryTrans =
                                    rightResultTransTvHelper.setText(
                                        rightHistoryTrans,
                                        currentTrans,
                                        true
                                    )
                            }
                        }
                    }

                    else -> {
                        when (textLiveIn) {
                            sourceLanguage -> {
                                leftHistoryContent =
                                    leftResultContentTvHelper.setText(
                                        leftHistoryContent,
                                        currentContent,
                                        false
                                    )
                                leftHistoryTrans =
                                    leftResultTransTvHelper.setText(
                                        leftHistoryTrans,
                                        currentTrans,
                                        false
                                    )
                            }

                            targetLanguage -> {
                                rightHistoryContent =
                                    rightResultContentTvHelper.setText(
                                        rightHistoryContent,
                                        currentContent,
                                        false
                                    )
                                rightHistoryTrans =
                                    rightResultTransTvHelper.setText(
                                        rightHistoryTrans,
                                        currentTrans,
                                        false
                                    )
                            }
                        }
                    }
                }
            }
        }
    }

    private suspend fun changeNetStatusUI(bool: Boolean) {
        withContext(Dispatchers.Main) {
            if (bool) {
                leftNetErrorTv.gone()
                if (showContent == SHOW_CONTENT_TRANS && transChildContent == TRANS_CHILD_TALK) {
                    middleLine.show()
                }
            } else {
                leftNetErrorTv.show()
                if (showContent == SHOW_CONTENT_TRANS && transChildContent == TRANS_CHILD_TALK) {
                    middleLine.gone()
                }
            }
        }
    }

    private fun changeVis(vis: Boolean) {
        logTagI(
            TAG,
            "changeVis $vis lastX $lastX lastY $lastY"
        )
        if (vis) {
            if (transcriptionCL.width == BIG_TRANS_WIDTH) {
                logTagI(TAG, "ai互译字幕已显示,return")
                return
            }
            val width = BIG_TRANS_WIDTH
            val height: Int = if (showContent == SHOW_CONTENT_TEXT) {
                BIG_TRANS_HEIGHT_TEXT
            } else {
                BIG_TRANS_HEIGHT
            }
            if (isFirstIn) {
                isFirstIn = false
                refreshParams(width, height, lastX, lastY)
            } else {
                // 播放显示动画（缩小动画的逆向）
                playShowAnimation(width, height, lastX, lastY)
            }

        } else {
            if (transcriptionCL.width == 1) {
                logTagI(TAG, "ai互译字幕已隐藏,return")
                return
            }
            makeDataText(isEndOfASentence = false, content = "", clearText = true)
            saveCurrentAndRefreshParams()

            // 如果不是用户主动隐藏（即正常结束），直接隐藏不播放动画
            if (!isUserHiding) {
                refreshParams(1, 1, 1, 1)
            }
            // 如果是用户主动隐藏，动画已经在点击事件中处理了
        }
    }

    private fun stopTrans(isLogin: Boolean = true) {
        TransHandler.isTranslating = false
        logStackTrace("song")
        val sessionId = AudioAiManager.getSessionId()
        logTagI(TAG, "00getSessionId--$sessionId")
        AudioAiManager.stopAsr(mAudioAiServiceCallback)
        logTagI(TAG, "11getSessionId--$sessionId")

        AudioAiManager.release()
        logTagI(TAG, "22getSessionId--$sessionId")

        // 格式化开始时间为年月日时分秒
        val sdf = SimpleDateFormat("yyyyMMddHHmm", Locale.getDefault())
        val formattedTime = sdf.format(Date(startTranslationTime))
        logTagI(TAG, "停止翻译，使用开始时间作为默认文件名: $formattedTime")

        if (isLogin) {
            val intent = Intent(this, AITransRenameDialogActivity::class.java).apply {
                putExtra("sessionId", sessionId)
                putExtra("defaultFileName", formattedTime)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)
        }

        stopSelf()
    }

    private fun saveCurrentAndRefreshParams() {
        lastX = wmParams!!.x
        lastY = wmParams!!.y
    }

    /**
     * 播放抖动动画
     * 提示用户悬浮窗已经显示
     * 整个框体抖动，干净利落
     */
    private fun playShakeAnimation() {
        logTagI(TAG, "播放悬浮窗抖动动画")

        // 获取当前窗口位置
        val currentX = (getScreenWidth() - windowWidth) / 2

        val shakeDistance = 15 // 抖动距离

        // 创建位置抖动动画：左-右-左-中心，一次性完成
        val shakeAnimator = ObjectAnimator.ofInt(
            currentX,
            currentX - shakeDistance,  // 向左
            currentX + shakeDistance,  // 向右
            currentX - shakeDistance / 2, // 向左（幅度减小）
            currentX + shakeDistance / 2, // 向右（幅度减小）
            currentX                   // 回到中心
        )

        shakeAnimator.duration = 300 // 干净利落
        shakeAnimator.addUpdateListener { animator ->
            wmParams!!.x = animator.animatedValue as Int
            windowManager.updateViewLayout(rootView, wmParams)
        }

        shakeAnimator.start()
    }

    /**
     * 播放隐藏动画
     * 逐步缩小到aiCCPlaceHolderView的中心点位置
     */
    private fun playHideAnimation(onAnimationEnd: () -> Unit) {
        logTagI(TAG, "播放悬浮窗隐藏动画")

        val currentX = wmParams!!.x
        val currentY = wmParams!!.y
        val windowWidth = transcriptionCL.width
        val windowHeight = transcriptionCL.height

        // 获取aiCCPlaceHolderView的中心点位置
        val targetCenterX = if (TransHandler.aiccPlaceholderX > 0) {
            TransHandler.aiccPlaceholderX// 偏移100像素
        } else {
//            resources.displayMetrics.widthPixels / 2 // 默认屏幕中央
            DEFAULT_AI_ICON_X
        }

        val targetCenterY = if (TransHandler.aiccPlaceholderY > 0) {
            TransHandler.aiccPlaceholderY
        } else {
            0 // 默认屏幕上方
        }

        // 计算悬浮窗左上角坐标，使其中心点对齐到目标中心点
        val targetX = targetCenterX - windowWidth / 2
        val targetY = 0

//        logTagI(
//            TAG,
//            "隐藏动画: 当前位置($currentX, $currentY), 窗口尺寸(${windowWidth}x${windowHeight})"
//        )
//        logTagI(
//            TAG,
//            "隐藏动画: 目标中心点($targetCenterX, $targetCenterY), 目标左上角($targetX, $targetY)"
//        )

        // 混合方案：X轴用pivot缩放，Y轴用平移动画
        // X轴：悬浮窗宽度可以覆盖目标X坐标，用pivot方式
        // Y轴：悬浮窗高度可能无法覆盖目标Y坐标，需要平移
//        val scaleX = W₂ / W₁
        val scaleXs = 130f / 1800f
//        val scaleY = H₂ / H₁
//        val pivotX = (X₁ - X₂) / (1 - scaleX)
        val pivotXs = abs((currentX.toFloat() - (targetCenterX.toFloat() - 65f)) / (1f - scaleXs))
//        val pivotY = (Y₁ - Y₂) / (1 - scaleY)

        // X轴：计算pivot点，以目标X坐标为缩放中心
        val pivotX = (targetCenterX - currentX) / windowWidth.toFloat()
        transcriptionCL.pivotX = pivotXs

        // Y轴：
        transcriptionCL.pivotY = 0F

        // 创建动画
        val scaleX = ObjectAnimator.ofFloat(transcriptionCL, "scaleX", 1f, scaleXs)
        val scaleY = ObjectAnimator.ofFloat(transcriptionCL, "scaleY", 1f, scaleXs)
        val alpha = ObjectAnimator.ofFloat(transcriptionCL, "alpha", 1f, 0f)

        // Y轴平移动画
        val moveY = ObjectAnimator.ofInt(currentY, targetY)
        moveY.addUpdateListener { animator ->
            wmParams!!.y = animator.animatedValue as Int
            windowManager.updateViewLayout(rootView, wmParams)
        }

        // 同步执行所有动画
        val finalAnimatorSet = AnimatorSet()
        finalAnimatorSet.playTogether(scaleX, scaleY, alpha, moveY)
        finalAnimatorSet.duration = 500

        // 动画结束监听器
        finalAnimatorSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                // 恢复原始状态
                transcriptionCL.scaleX = 1f
                transcriptionCL.scaleY = 1f
                transcriptionCL.alpha = 1f
                transcriptionCL.translationX = 0f

                // 恢复默认的缩放中心点（View中心）
                transcriptionCL.pivotX = windowWidth / 2f
                transcriptionCL.pivotY = windowHeight / 2f

                logTagI(TAG, "动画结束，X轴pivot已恢复，Y轴位置已到达目标")

                // 隐藏悬浮窗
                refreshParams(1, 1, 1, 1)

                // 执行回调
                onAnimationEnd()
            }
        })

        // 启动分阶段动画
        finalAnimatorSet.start()
    }

    /**
     * 播放显示动画
     * 从aiCCPlaceHolderView的中心点位置逐步放大到正常尺寸
     * 这是隐藏动画的完全逆向过程，使用相同的pivot缩放逻辑
     */
    private fun playShowAnimation(targetWidth: Int, targetHeight: Int, targetX: Int, targetY: Int) {
        logTagI(TAG, "播放悬浮窗显示动画")

        // 获取aiCCPlaceHolderView的中心点位置（与隐藏动画的目标中心点相同）
        val startCenterX = if (TransHandler.aiccPlaceholderX > 0) {
            TransHandler.aiccPlaceholderX
        } else {
            DEFAULT_AI_ICON_X
        }

        val startCenterY = if (TransHandler.aiccPlaceholderY > 0) {
            TransHandler.aiccPlaceholderY
        } else {
            0 // 默认屏幕上方
        }

        val aiccPlaceholderWidth = 130f

        val scaleXs = aiccPlaceholderWidth / BIG_TRANS_WIDTH.toFloat()
        val pivotXs = (startCenterX - aiccPlaceholderWidth / 2 - BIG_TRANS_X) / (1 - scaleXs)

        // 计算动画起始位置（悬浮窗左上角坐标，与隐藏动画的目标位置相同）
        val startX = BIG_TRANS_X
        val startY = 0

        // 先设置悬浮窗到目标尺寸和起始位置
        refreshParams(targetWidth, targetHeight, BIG_TRANS_X, 0)

        // 设置缩放中心点：以起始中心点为缩放中心（与隐藏动画逆向）
        // 计算pivot点，使缩放中心在起始中心点位置
        val pivotX = (startCenterX - startX) / targetWidth.toFloat()
        transcriptionCL.pivotX = pivotXs
        transcriptionCL.pivotY = 0f

        // 设置初始动画状态（缩小、透明）
        transcriptionCL.scaleX = scaleXs
        transcriptionCL.scaleY = scaleXs

        // 创建动画：从小到大、从透明到不透明、从起始位置到目标位置
        val scaleX = ObjectAnimator.ofFloat(transcriptionCL, "scaleX", scaleXs, 1f)
        val scaleY = ObjectAnimator.ofFloat(transcriptionCL, "scaleY", scaleXs, 1f)
        val alpha = ObjectAnimator.ofFloat(transcriptionCL, "alpha", 0.5F, 1f)

        // Y轴平移动画：从起始Y位置移动到目标Y位置（与隐藏动画逆向）
        val moveY = ObjectAnimator.ofInt(startY, targetY)
        moveY.addUpdateListener { animator ->
            wmParams!!.y = animator.animatedValue as Int
            windowManager.updateViewLayout(rootView, wmParams)
        }

        // 同步执行所有动画
        val showAnimatorSet = AnimatorSet()
        showAnimatorSet.playTogether(scaleX, scaleY, alpha, moveY)
        showAnimatorSet.duration = 600
        showAnimatorSet.interpolator = AccelerateDecelerateInterpolator() // 使用加速减速插值器

        // 动画结束监听器
        showAnimatorSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                // 确保最终状态正确
                transcriptionCL.scaleX = 1f
                transcriptionCL.scaleY = 1f
                transcriptionCL.alpha = 1f

                // 恢复默认的缩放中心点（View中心）
                transcriptionCL.pivotX = targetWidth / 2f
                transcriptionCL.pivotY = targetHeight / 2f

                // 确保最终位置正确
//                refreshParams(targetWidth, targetHeight, targetX, targetY)

                logTagI(TAG, "显示动画结束，悬浮窗已完全显示")
            }
        })

        // 启动显示动画
        showAnimatorSet.start()
    }

}




