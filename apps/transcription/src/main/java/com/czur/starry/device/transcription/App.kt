package com.czur.starry.device.transcription

import android.app.Application
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.listener.StarryApp
import kotlinx.coroutines.flow.MutableSharedFlow

/**
 * Created by 陈丰尧 on 2022/8/18
 */
class App : StarryApp() {

    // 需要跨Aty使用, 表示全部退出的信号
    val finishAppFlow = MutableSharedFlow<Long>()
    override fun onCreate() {
        super.onCreate()
        instance = this
    }

    companion object {
        var instance: Application? = null
    }

}