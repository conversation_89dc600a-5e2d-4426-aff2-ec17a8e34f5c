package com.czur.starry.device.transcription.dialog

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.view.Gravity
import android.view.KeyEvent
import android.view.View
import android.view.WindowManager
import com.czur.czurutils.log.logIntent
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.getTopControlBarHeight
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.widget.CommonButton
import com.czur.starry.device.transcription.R
import com.czur.starry.device.transcription.databinding.DialogCommonBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

open class AITransWarnTimeActivity() : CZViewBindingAty<DialogCommonBinding>() {
    companion object {
        const val DOUBLE_FLOAT_BTN_CONFIRM = 1
        const val DOUBLE_FLOAT_BTN_CANCEL = 0
        const val TAG = "AITransWarnTimeDialogActivity"
    }

    // 标题
    private var title: String =
        com.czur.starry.device.baselib.utils.getString(com.czur.starry.device.baselib.R.string.dialog_normal_title_tips)

    // 内容
    var content: String = com.czur.starry.device.baselib.utils.getString(R.string.str_translation_will_stop_20min)

    // 内容（标注）
    var notesContent: String = com.czur.starry.device.baselib.utils.getString(R.string.str_translation_will_stop_10sec, "10")

    // 按钮
    var cancelBtnText: String =
        com.czur.starry.device.baselib.utils.getString(R.string.dialog_normal_cancel)
    var confirmBtnText: String =
        com.czur.starry.device.baselib.utils.getString(R.string.dialog_normal_confirm)
    var confirmTheme: CommonButton.Theme = CommonButton.Theme.WHITE2
    var outSideDismiss: Boolean = false

    var onCommonClick: ((position: Int) -> Unit)? = null

    var startTranslationTime = 0L
    private var closeCountDownJob: Job? = null

    var leftTime: Long = 0L
    private fun startTransDurationWatchJob() {
        closeCountDownJob = launch(Dispatchers.IO) {
            while (true) {
                // 计算时间,取整秒
                val time = SystemClock.elapsedRealtime() - startTranslationTime
                // time变成秒的整数
                val timeInSecond = time / 1000
                val timeStr = 10 - timeInSecond
                if (timeStr < 1) {
                    <EMAIL>()
                    return@launch
                }
                withContext(Dispatchers.Main) {
                    binding.doubleBtnFloatNotesContentTv.visibility = View.VISIBLE
                    binding.doubleBtnFloatNotesContentTv.text =
                        getString(R.string.str_translation_will_stop_10sec, timeStr.toString())
                }

                delay(300) // 每10秒检查一次
            }
        }
    }


    private var startTime = 0L
    override fun initWindow() {
        super.initWindow()

        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus) {
            logTagV(TAG, "适配StudioSPlus")
            window?.apply {
                val params = attributes
                params?.y = getTopControlBarHeight() / 2
                attributes = params
                setGravity(Gravity.CENTER)
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handlePreIntent(intent)


    }

    override fun handlePreIntent(preIntent: Intent) {
        logIntent(preIntent, TAG)

    }


    override fun DialogCommonBinding.initBindingViews() {
        setupCommonDialog()
        startTransDurationWatchJob()
    }

    protected open fun DialogCommonBinding.setupCommonDialog() {
        logTagI(TAG, "打开20分钟时间警告窗口")
        startTime = SystemClock.elapsedRealtime()
        setFinishOnTouchOutside(outSideDismiss)  // 禁止点击空白部分退出
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {   //对于Android 8.0及以上
            window.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY);
        } else {
            window.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT);
        }

        doubleBtnFloatTitleTv.text = title
        doubleBtnFloatContentTv.text = content
        if (notesContent == "") {
            doubleBtnFloatNotesContentTv.visibility = View.GONE
        } else {
            doubleBtnFloatNotesContentTv.visibility = View.VISIBLE
            doubleBtnFloatNotesContentTv.text = notesContent
        }
        doubleBtnFloatConfirmBtn.text = confirmBtnText
        doubleBtnFloatConfirmBtn.changeTheme(confirmTheme)

        doubleBtnFloatConfirmBtn.setOnClickListener {
            finish()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        startTranslationTime = SystemClock.elapsedRealtime()
        startTransDurationWatchJob()
    }

    override fun finish() {
        super.finish()
        startTime -= 1000 // 减去1秒，避免误触发
        logTagI(TAG, "20分钟时间警告窗口finish")
    }

    override fun onDestroy() {
        super.onDestroy()
        logTagI(TAG, "20分钟时间警告窗口onDestroy ${SystemClock.elapsedRealtime() - startTime}")
        if (SystemClock.elapsedRealtime() - startTime < 1000) {
            logTagW(TAG, "20分钟时间警告窗口onDestroy过快，可能是误触发，重新启动")
            startActivity(intent)
            return
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            finish()
            true
        } else {
            false
        }
    }

}