package com.czur.starry.device.update.utils

import com.czur.czurutils.log.logTagI
import android.content.Context
import android.os.CountDownTimer
import android.os.Handler
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import com.czur.starry.device.update.R

class ToastUtil(context: Context?, success: Boolean) {
    companion object{
         var mToast: Toast? = null
         var canceled = true
    }
    private var context :Context? = null
    private var success :Boolean? = null
    private var mTextView: TextView? = null
    private var timeCount: TimeCount? = null
    private val message: String? = null
    private val mHandler = Handler()

    init {
        this.context = context
        this.success = success
        initView()
    }
    private fun initView(){

//        message = msg;
        val inflater = context!!.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        //自定义布局
        val view: View = inflater.inflate(R.layout.toast_layout, null)
        //自定义toast文本
        mTextView = view.findViewById(R.id.tv_msg) as TextView

        val image = view.findViewById(R.id.bg_imge) as ImageView
        if (success!!) {
            image.background = context!!.getDrawable(R.drawable.update_success)
            mTextView!!.setText(context!!.getString(R.string.update_success))
        } else {
            image.background = context!!.getDrawable(R.drawable.update_failed)
            mTextView!!.setText(context!!.getString(R.string.update_failed))
        }

        logTagI("ToastUtil", "Toast start...")
        if (mToast == null) {
            mToast = Toast(context)
            logTagI("ToastUtil", "Toast create...")
        }
        //设置toast居中显示
        mToast!!.setGravity(Gravity.FILL, 0, 0)
        mToast!!.setDuration(Toast.LENGTH_LONG)
        mToast!!.setView(view)
    }

    /**
     * 自定义时长、居中显示toast
     * @param duration
     */
    fun show(duration: Int) {
        timeCount = TimeCount(duration.toLong(), 1000)
        logTagI("ToastUtil", "Toast show...")
        if (canceled) {
            timeCount!!.start()
            canceled = false
            showUntilCancel()
        }
    }

    /**
     * 隐藏toast
     */
    private fun hide() {
        if (mToast != null) {
            mToast!!.cancel()
        }
        canceled = true
        logTagI("ToastUtil", "Toast that customed duration hide...")
    }

    private fun showUntilCancel() {
        if (canceled) { //如果已经取消显示，就直接return
            return
        }
        mToast!!.show()
        mHandler.postDelayed({
//            logTagI("ToastUtil", "Toast showUntilCancel...")
            showUntilCancel()
        }, Toast.LENGTH_LONG.toLong())
    }
    /**
     * 自定义计时器
     */
    private class TimeCount(millisInFuture: Long, countDownInterval: Long) :
        CountDownTimer(millisInFuture, countDownInterval) {
        override fun onTick(millisUntilFinished: Long) {
//            mTextView.setText(message + ": " + millisUntilFinished / 1000 + "s后消失");
        }

        override fun onFinish() {
//            hide()
            if (mToast != null) {
                mToast!!.cancel()
            }
            canceled = true
        }
    }
}