<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:background="#5879FC">

    <ImageView
        android:id="@+id/ivIcon"
        android:layout_width="180px"
        android:layout_height="180px"
        android:layout_marginTop="350px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTile"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40px"
        android:text="@string/update_verify"
        android:textColor="@color/white"
        android:textSize="44px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivIcon" />

    <TextView
        android:id="@+id/tvWarning"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="65px"
        android:text="@string/update_verify_warning"
        android:textColor="@color/white"
        android:textSize="30px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTile" />
</androidx.constraintlayout.widget.ConstraintLayout>