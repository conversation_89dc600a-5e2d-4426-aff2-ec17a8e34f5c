package com.czur.starry.device.starrypad.ui.ota

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.databinding.FragmentTransOtaBinding
import com.czur.starry.writepadlib.proto.WPTransferData.UpgradeInfo.UpgradeTransMsg

/**
 * Created by 陈丰尧 on 2023/12/20
 */
private const val TAG = "TransOTAFragment"

class TransOTAFragment : CZViewBindingFragment<FragmentTransOtaBinding>() {
    private val otaViewModel: WPOtaViewModel by activityViewModels()
    override fun FragmentTransOtaBinding.initBindingViews() {
        transOTAPb.max = 100
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatCollectOnResume(otaViewModel.transPercentFlow) {
            binding.transOTAPb.progress = it.toLong()
            binding.transPercentTv.text = it.toString().padStart(2, '0')
        }

        repeatCollectOnResume(otaViewModel.remoteUpgradeInfoFlow) {
            if (it.hasUpgradeRequest()) {
                logTagD(TAG, "客户端准备好接受升级包")
                otaViewModel.startTransOta()
            } else if (it.hasUpgradeTransMsg()) {
                val msg = it.upgradeTransMsg
                logTagD(TAG, "收到升级包传输消息: $msg")
                when (msg) {
                    UpgradeTransMsg.CLI_SUCCESS -> {
                        // 升级完成, 退出页面
                        logTagI(TAG, "升级包传输完成")
                        requireActivity().finish()
                    }

                    UpgradeTransMsg.CLI_ERROR -> {
                        // 升级失败, 退出页面
                        toast(R.string.toast_ota_trans_fail)
                        requireActivity().finish()
                    }

                    else -> {
                        logTagE(TAG, "未知的升级包传输消息: $msg")
                    }
                }
            }
        }

        repeatCollectOnResume(otaViewModel.otaErrorFlow) {
            // 升级失败, 退出页面
            toast(R.string.toast_ota_trans_fail)
            activity?.finish()
        }

        if (!otaViewModel.hasConnectDev) {
            // 升级失败, 退出页面
            toast(R.string.toast_ota_trans_fail)
            activity?.finish()
            return
        }

        launch {
            otaViewModel.pushUpgradeRequest()
        }
    }
}