package com.czur.starry.device.starrypad.hardware

import android.annotation.SuppressLint
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import androidx.core.app.NotificationCompat
import androidx.lifecycle.LifecycleService
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_WP_SCREEN_ON
import com.czur.starry.device.baselib.common.VALUE_WP_SCREEN_OFF
import com.czur.starry.device.baselib.common.VALUE_WP_SCREEN_ON
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.CZPowerManager
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.createNotificationChannel
import com.czur.starry.device.baselib.utils.keyboard.injectKeyAPPSwitch
import com.czur.starry.device.baselib.utils.keyboard.injectKeyBack
import com.czur.starry.device.baselib.utils.keyboard.injectKeyHome
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.setSystemProp
import com.czur.starry.device.baselib.utils.repeatCollectOnStart
import com.czur.starry.device.bluetoothlib.util.unpair
import com.czur.starry.device.noticelib.hwconn.HwConnHelper
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager
import com.czur.starry.device.starrypad.hardware.trans.bt.BTServer
import com.czur.starry.device.starrypad.hardware.trans.netty.WritePadNettyServer
import com.czur.starry.device.starrypad.receiver.ShutdownReceiver
import com.czur.starry.device.starrypad.ui.window.GlobalMarkService
import com.czur.starry.device.starrypad.ui.window.PaletteService
import com.czur.starry.writepadlib.proto.WPTransferData
import com.czur.starry.writepadlib.proto.WPTransferData.ProcessControl.ControlEventCase.KEYEVENT
import com.czur.starry.writepadlib.proto.WPTransferData.ProcessControl.ControlEventCase.PROCESSEVENT
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged

/**
 * Created by 陈丰尧 on 2023/8/31
 * 手写板服务, 主要负责
 * 1. 蓝牙Service: 每当有蓝牙客户端接入时, 都会向其发送设备的AP信息
 * 2. 启动NettyService
 */
private const val TAG = "WritePadService"

class WritePadService : LifecycleService() {
    private var btServer: BTServer = BTServer
    private val nettyServer by lazy {
        WritePadNettyServer(WritePadDeviceModeManager)
    }
    private val devInfoManager = WPDeviceInfoManager

    private val shutDownReceiver = ShutdownReceiver()   // 关机广播
    private val touchBarEventReceiver by lazy(LazyThreadSafetyMode.NONE) {
        TouchBarEventReceiver()
    }

    // 自动重连相关
    private val bluetoothManager by lazy { getSystemService(BluetoothManager::class.java) }
    private val bluetoothAdapter by lazy { bluetoothManager?.adapter }
    private val reconnectJobs = mutableMapOf<String, Job>() // 存储每个设备的重连任务

    companion object {
        private const val CHANNEL_ID = "WritePadService"
        private const val MAX_RECONNECT_ATTEMPTS = 5 // 最大重连次数
        private const val RECONNECT_DELAY_MS = 3000L // 重连间隔3秒
        private const val RECONNECT_BACKOFF_MULTIPLIER = 1.5f // 退避倍数
    }

    override fun onCreate() {
        super.onCreate()
        logTagD(TAG, "WritePadService onCreate")
        launch {
            devInfoManager.loadWhiteListFromDisk()
        }
        showNotification()  // 不添加通知栏, 批注模式会很慢

        startBtServer()

        launch {
            nettyServer.startServer()
            WritePadDeviceModeManager.nettySender = nettyServer
        }

        shutDownReceiver.register(this)
        // StudioSPlus才有控制栏
        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus) {
            touchBarEventReceiver.register(this)
        }

        repeatCollectOnStart(WritePadDeviceModeManager.serverModeReqFlow) {
            logTagD(TAG, "收到模式切换请求: $it")
            when (it) {
                WPTransferData.Mode.MODE_MARK -> {
                    if (WritePadDeviceModeManager.isBootingDrawService) {
                        logTagD(TAG, "正在启动, 忽略")
                        return@repeatCollectOnStart
                    }
                    CZPowerManager.wakeUpScreen("Boot GlobalMarkService")
                    // 亮屏后3秒内不能截图, 防止截图失败
                    val delayTime = (3 * ONE_SECOND) - CZPowerManager.screenOnDuration
                    if (delayTime > 0) {
                        logTagD(TAG, "等待屏幕唤醒: $delayTime")
                        delay(delayTime)
                    }
                    logTagD(TAG, "启动GlobalMarkService")
                    GlobalMarkService.start(this@WritePadService)
                }

                WPTransferData.Mode.MODE_PALETTE -> {
                    if (WritePadDeviceModeManager.isBootingDrawService) {
                        logTagD(TAG, "正在启动, 忽略")
                        return@repeatCollectOnStart
                    }
                    logTagD(TAG, "启动画板")
                    PaletteService.start(this@WritePadService)
                }

                else -> {}
            }
        }

        repeatCollectOnStart(WritePadDeviceModeManager.serverModeFlow) {
            logTagD(TAG, "广播serverMode: $it")
            WritePadDeviceModeManager.sendMsg(ServerProtoBuilder.withServerMode {
                mode = it
            })
        }

        /**
         * 设备信息更新
         */
        repeatCollectOnStart(WritePadDeviceModeManager.clientInfoFlow) {

            if (devInfoManager.isNewConnDev(it.name)) {
                logTagI(TAG, "新连接设备: ${it.name}")
                HwConnHelper.showHwConnAlert(
                    this@WritePadService,
                    HwConnHelper.HwConnType.WRITE_PAD
                )
            }
            devInfoManager.updateDevInfoFromNetty(it)

            launch {
                val devOtaInfo = devInfoManager.getOATVersion(it)
                if (!devOtaInfo.downloadUrl.isNullOrBlank() && !devOtaInfo.md5.isNullOrBlank()) {
                    // 有OTA信息
                    val withOTAInfo = it.copy(otaInfo = devOtaInfo)
                    devInfoManager.updateDevInfoFromNetty(withOTAInfo)
                }
            }

        }

        repeatCollectOnStart(
            WPDeviceInfoManager.hasDevicesScreenOnFlow.distinctUntilChanged().conflate()
                .debounce(20)
        ) {
            logTagD(TAG, "hasDevicesScreenOnFlow:${it}")
            setSystemProp(KEY_WP_SCREEN_ON, if (it) VALUE_WP_SCREEN_ON else VALUE_WP_SCREEN_OFF)
        }

        repeatCollectOnStart(WritePadDeviceModeManager.remoteModeChangeReqFlow) {
            logTagD(TAG, "收到远端模式切换请求: ${it.mode}")
            WritePadDeviceModeManager.updateClientModeReq(it)
        }

        repeatCollectOnStart(WritePadDeviceModeManager.remoteProcessControlFlow) {
            logTagD(TAG, "收到远端流程控制请求: $it")
            when (it.controlEventCase) {
                PROCESSEVENT -> {
                    val processEvent = it.processEvent
                    when (processEvent) {
                        WPTransferData.ProcessControlEvent.STARRY_SCREEN_ON -> {
                            CZPowerManager.wakeUpScreen("WPClientReq")
                        }

                        WPTransferData.ProcessControlEvent.REQ_SYNC_SERVER_MODE -> {
                            val devId = it.devID
                            logTagD(TAG, "客户端请求同步serverMode:${it.devID}")
                            WritePadDeviceModeManager.sendMsg(ServerProtoBuilder.withServerMode {
                                mode = WritePadDeviceModeManager.serverMode
                            }, devId)
                        }

                        WPTransferData.ProcessControlEvent.REQ_RE_PAIR -> {
                            val devId = it.devID
                            logTagD(TAG, "客户端请求重新配对: $devId")
                            WPDeviceInfoManager.removeClientBtName(devId)

                        }

                        else -> {}
                    }
                }

                KEYEVENT -> {
                    val keyEvent = it.keyEvent
                    when (keyEvent) {
                        WPTransferData.StarryKeyEvent.KEY_BACK -> {
                            logTagV(TAG, "注入返回键(手写板发送)")
                            injectKeyBack()
                        }

                        WPTransferData.StarryKeyEvent.KEY_HOME -> {
                            logTagV(TAG, "注入Home键(手写板发送)")
                            injectKeyHome()
                        }

                        WPTransferData.StarryKeyEvent.KEY_APP_SWITCH -> {
                            logTagV(TAG, "注入多任务键(手写板发送)")
                            injectKeyAPPSwitch()
                        }

                        else -> {}
                    }
                }

                else -> {}
            }
        }
    }

    /**
     * 启动蓝牙服务端, 像客户端发送设备的AP信息
     */
    @SuppressLint("MissingPermission")
    private fun startBtServer() {
        btServer.registerBTBondChangeReceiver(this)

        // 设置设备解绑监听器
        btServer.onUserDeUnbindListener = { dev ->
            val devName = dev.name ?: ""
            logTagD(TAG, "用户解绑设备蓝牙: $devName")
            launch {
                if (WPDeviceInfoManager.isLegalDevice(devName)) {
                    logTagW(TAG," 用户解绑设备: $devName, 设备信息未被删除")
                    // 启动自动重连
                    startAutoReconnect(dev)
                }
            }
        }

        // 设置蓝牙重连成功的回调
        setupBluetoothReconnectionCallback()
    }

    /**
     * 设置蓝牙重连成功的回调
     */
    @SuppressLint("MissingPermission")
    private fun setupBluetoothReconnectionCallback() {
        // 设置蓝牙配对状态变化监听器，用于检测设备重连成功
        btServer.onBondStateChangedListener = { device, bondState ->
            onBluetoothDeviceBondStateChanged(device, bondState)
        }
    }

    /**
     * 处理蓝牙设备配对状态变化
     * 这个方法应该在BTBoundChangeReceiver中被调用
     */
    @SuppressLint("MissingPermission")
    fun onBluetoothDeviceBondStateChanged(device: BluetoothDevice, bondState: Int) {
        val deviceName = device.name ?: ""
        val deviceAddress = device.address

        when (bondState) {
            BluetoothDevice.BOND_BONDED -> {
                // 设备重新配对成功
                if (reconnectJobs.containsKey(deviceAddress)) {
                    logTagI(TAG, "设备 $deviceName 重新配对成功，停止自动重连任务")
                    stopAutoReconnect(deviceAddress)
                }
            }
            BluetoothDevice.BOND_NONE -> {
                // 设备解绑，这个在onUserDeUnbindListener中已经处理了
                logTagD(TAG, "设备 $deviceName 已解绑")
            }
            BluetoothDevice.BOND_BONDING -> {
                // 设备正在配对中
                logTagD(TAG, "设备 $deviceName 正在配对中...")
            }
        }
    }

    private fun showNotification() {
        createNotificationChannel(CHANNEL_ID, CHANNEL_ID)
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(TAG)
            .setContentText(TAG)
            .setSmallIcon(R.mipmap.ic_launcher)
            .build()
        startForeground(4, notification)
    }

    /**
     * 启动自动重连
     * @param device 需要重连的蓝牙设备
     */
    @SuppressLint("MissingPermission")
    private fun startAutoReconnect(device: BluetoothDevice) {
        val deviceName = device.name ?: ""
        val deviceAddress = device.address

        logTagI(TAG, "启动自动重连: $deviceName ($deviceAddress)")

        // 取消之前的重连任务
        reconnectJobs[deviceAddress]?.cancel()

        // 启动新的重连任务
        reconnectJobs[deviceAddress] = launch {
            var attemptCount = 0
            var currentDelay = RECONNECT_DELAY_MS

            while (attemptCount < MAX_RECONNECT_ATTEMPTS) {
                attemptCount++
                logTagI(TAG, "尝试重连 $deviceName, 第 $attemptCount/$MAX_RECONNECT_ATTEMPTS 次")

                try {
                    // 等待一段时间再重连
                    delay(currentDelay)

                    // 检查蓝牙是否可用
                    if (bluetoothAdapter?.isEnabled != true) {
                        logTagW(TAG, "蓝牙未启用，跳过重连")
                        continue
                    }

                    // 尝试重新配对
                    val success = attemptReconnect(device)
                    if (success) {
                        logTagI(TAG, "设备 $deviceName 重连成功")
                        reconnectJobs.remove(deviceAddress)
                        return@launch
                    } else {
                        logTagW(TAG, "设备 $deviceName 重连失败，第 $attemptCount 次尝试")
                    }

                } catch (e: Exception) {
                    logTagW(TAG, "重连过程中发生异常: ${e.message}", tr = e)
                }

                // 增加延迟时间（指数退避）
                currentDelay = (currentDelay * RECONNECT_BACKOFF_MULTIPLIER).toLong()
            }

            logTagW(TAG, "设备 $deviceName 重连失败，已达到最大重试次数")
            reconnectJobs.remove(deviceAddress)
        }
    }

    /**
     * 尝试重新连接设备
     * @param device 要重连的设备
     * @return 是否成功
     */
    @SuppressLint("MissingPermission")
    private suspend fun attemptReconnect(device: BluetoothDevice): Boolean {
        return try {
            val deviceName = device.name ?: ""
            logTagD(TAG, "开始重新配对设备: $deviceName")

            // 检查设备是否仍在合法设备列表中
            if (!WPDeviceInfoManager.isLegalDevice(deviceName)) {
                logTagW(TAG, "设备 $deviceName 不在合法设备列表中，停止重连")
                return false
            }

            // 尝试重新配对
            val pairResult = device.createBond()
            if (pairResult) {
                logTagI(TAG, "设备 $deviceName 配对请求已发送")

                // 等待配对完成（最多等待10秒）
                var waitTime = 0
                while (waitTime < 10000 && device.bondState == BluetoothDevice.BOND_BONDING) {
                    delay(500)
                    waitTime += 500
                }

                val finalBondState = device.bondState
                logTagD(TAG, "设备 $deviceName 最终配对状态: $finalBondState")

                return finalBondState == BluetoothDevice.BOND_BONDED
            } else {
                logTagW(TAG, "设备 $deviceName 配对请求发送失败")
                return false
            }

        } catch (e: Exception) {
            logTagW(TAG, "重连设备时发生异常: ${e.message}", tr = e)
            false
        }
    }

    /**
     * 停止指定设备的自动重连
     * @param deviceAddress 设备地址
     */
    private fun stopAutoReconnect(deviceAddress: String) {
        reconnectJobs[deviceAddress]?.cancel()
        reconnectJobs.remove(deviceAddress)
        logTagD(TAG, "已停止设备 $deviceAddress 的自动重连")
    }

    /**
     * 停止所有自动重连任务
     */
    private fun stopAllAutoReconnect() {
        reconnectJobs.values.forEach { it.cancel() }
        reconnectJobs.clear()
        logTagD(TAG, "已停止所有自动重连任务")
    }

    override fun onDestroy() {
        logTagW(TAG, "WritePadService onDestroy")

        // 停止所有自动重连任务
        stopAllAutoReconnect()

        shutDownReceiver.unregister(this)
        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus) {
            touchBarEventReceiver.unregister(this)
        }

        btServer.unregisterBTBondChangeReceiver(this)
        val expHandler = CoroutineExceptionHandler { _, throwable ->
            logTagW(TAG, "WritePadService 释放资源失败", tr = throwable)
        }
        launch(expHandler) {
            WritePadDeviceModeManager.nettySender = null
            nettyServer.stopServer()
        }

        super.onDestroy()
    }
}