package com.czur.starry.device.starrypad.vm

import android.app.Application
import androidx.lifecycle.*
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.starrypad.db.entity.AlbumEntity
import com.czur.starry.device.starrypad.db.entity.AlbumWithPaintList
import com.czur.starry.device.starrypad.db.entity.PaintEntity
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager
import com.czur.starry.device.starrypad.repository.PaintRepository
import com.czur.starry.device.starrypad.ui.adapter.HistoryPaintEntityVO
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn

/**
 * Created by 陈丰尧 on 2023/1/12
 */
class HistoryAlbumViewModel(application: Application) : AndroidViewModel(application) {
    private companion object {
        private const val TAG = "HistoryAlbumViewModel"
    }

    /**
     * 当前选中索引
     */
    var currentIndex: Int = 0
        set(value) {
            val needUpdateVOList = field != value
            field = value
            currentPaintEntity?.let {
                (currentPaintPath as MutableLiveData).value = it.contentImgPath
            }
            if (needUpdateVOList) {
                updateHistoryPaintListVO()
            }
        }

    // 当前相册, 为空时表示相册没了
    val currentAlbumLive: LiveData<AlbumEntity?> = MutableLiveData()

    // 当前选中的笔记
    val currentPaintEntity: PaintEntity?
        get() = paintList.getOrNull(currentIndex)

    // 当前展示的笔记图片路径
    val currentPaintPath: LiveData<String> = MutableLiveData()

    val historyPaintListLive: LiveData<List<HistoryPaintEntityVO>> = MutableLiveData()
    private var historyPaintList by LiveDataDelegate(historyPaintListLive, emptyList())

    private var paintList: MutableList<PaintEntity> = mutableListOf()

    var modifyTime = System.currentTimeMillis()
        private set
    var hasDel = false
        private set

    // 画册中笔记数量
    val paintCountLive = historyPaintListLive.map {
        if (it.size > 99) "99+" else it.size.toString()
    }.distinctUntilChanged()


    fun initAlbumData(albumWithPaintList: AlbumWithPaintList) {
        (currentAlbumLive as MutableLiveData).value = albumWithPaintList.album
        paintList = albumWithPaintList.paintList.toMutableList()
        currentIndex = 0
        updateHistoryPaintListVO()
    }

    /**
     * 更新相册的打开时间
     */
    suspend fun updateOpenTime(album: AlbumEntity) {
        PaintRepository.updateOpenTime(album)
    }

    /**
     * 删除当前的笔记
     */
    suspend fun delCurrent() {
        logTagD(TAG, "删除当前笔记")
        val currentPaint = currentPaintEntity ?: return
        if (paintList.size <= 1) {
            logTagD(TAG, "删除整个相册")
            PaintRepository.delAlbums(currentAlbumLive.value ?: return)
            (currentAlbumLive as MutableLiveData).value = null
        } else {
            logTagD(TAG, "删除单张图片")
            PaintRepository.delPaint(currentPaint)
            paintList.removeAt(currentIndex)
            if (currentIndex != paintList.size) {
                logTagD(TAG, "需要重新调整文件存放位置")
                paintList = PaintRepository.rePositionPaints(paintList).toMutableList()
            }
            modifyTime = System.currentTimeMillis() // 更新时间
            hasDel = true
            if (currentIndex >= paintList.size) {
                // 后面没有了, 就向前移动
                currentIndex--
            } else {
                logTagD(TAG, "触发一次更新")
                currentIndex = currentIndex
                updateHistoryPaintListVO()
            }
        }
    }

    private fun updateHistoryPaintListVO() {
        historyPaintList = paintList.mapIndexed { index, paintEntity ->
            HistoryPaintEntityVO(
                paintEntity,
                modifyTime,
                index == currentIndex
            )
        }
    }
}