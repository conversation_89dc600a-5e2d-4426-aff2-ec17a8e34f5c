package com.czur.starry.device.starrypad.ui.ota

import android.os.Bundle
import android.os.SystemClock
import androidx.fragment.app.activityViewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.utils.toastFail
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.common.WPOTAConstant
import com.czur.starry.device.starrypad.databinding.FragmentDownloadOtaBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2023/12/19
 */
private const val TAG = "DownloadOTAFragment"
private const val CHECK_SIZE_INTERVAL = 3 * ONE_SECOND

class DownloadOTAFragment : CZViewBindingFragment<FragmentDownloadOtaBinding>() {
    private val otaViewModel: WPOtaViewModel by activityViewModels()
    private var lastCheckSizeTime = 0L
    private var downloadJob: Job? = null

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatCollectOnResume(otaViewModel.downloadProgressStrFlow) {
            binding.downloadProgressTv.text = it
        }
        repeatCollectOnResume(otaViewModel.downloadCurrentSizeFlow) {
            binding.downloadPb.progress = it
            checkFreeSize()
        }
        repeatCollectOnResume(otaViewModel.downloadTotalSizeFlow) {
            binding.downloadPb.max = it
        }

        repeatCollectOnResume(otaViewModel.downloadCurrentStrFlow) {
            binding.downSizeTv.text = it
        }

        repeatCollectOnResume(otaViewModel.downloadTotalStrFlow) {
            binding.sumSizeTv.text = it
        }

        downloadJob = launch {
            logTagD(TAG, "开始下载WP OTA文件")
            val downloadFile = otaViewModel.downloadOtaPkg()
            if (isActive.not()) {
                logTagD(TAG, "下载WP OTA包被取消")
                return@launch
            }

            if (downloadFile == null) {
                val freeSize = withContext(Dispatchers.IO) {
                    requireActivity().cacheDir.freeSpace
                }
                if (freeSize < WPOTAConstant.OTA_MINIMUM_RESERVED_STORAGE_CAPACITY) {
                    logTagW(
                        TAG,
                        "下载WP OTA包失败，空间不足,当前剩余空间:${freeSize}, 需要空间:${WPOTAConstant.OTA_MINIMUM_RESERVED_STORAGE_CAPACITY}"
                    )
                    toast(R.string.toast_write_pad_ota_no_space)
                } else {
                    toast(R.string.toast_download_ota_failed)
                }
                logTagE(TAG, "下载WP OTA包失败")
                requireActivity().finish()
                return@launch
            }

            val md5Check = otaViewModel.checkMd5(downloadFile)
            logTagD(TAG, "md5校验结果:${md5Check}")

            if (!md5Check) {
                withContext(Dispatchers.IO) {
                    downloadFile.delete()
                }
                requireActivity().finish()
                return@launch
            }

            val newName = otaViewModel.renameOTAFile(downloadFile)
            if (newName == null) {
                toastFail()
                logTagE(TAG, "重命名失败")
                requireActivity().finish()
                return@launch
            }
            // 切换到传输页面
            otaViewModel.changeTransPage(newName)
        }
    }

    /**
     * 检查剩余空间
     */
    private fun checkFreeSize() {
        if (SystemClock.elapsedRealtime() - lastCheckSizeTime < CHECK_SIZE_INTERVAL) {
            // 检查时间太短
            return
        }
        lastCheckSizeTime = SystemClock.elapsedRealtime()
        launch {
            withContext(Dispatchers.IO) {
                val freeSize = requireActivity().cacheDir.freeSpace
                logTagV(TAG, "当前剩余空间:${freeSize}")
                if (freeSize < WPOTAConstant.OTA_MINIMUM_RESERVED_STORAGE_CAPACITY) {
                    logTagW(
                        TAG,
                        "下载WP OTA包失败，空间不足,当前剩余空间:${freeSize}, 需要空间:${WPOTAConstant.OTA_MINIMUM_RESERVED_STORAGE_CAPACITY}"
                    )
                    downloadJob?.cancel()
                    withContext(Dispatchers.Main) {
                        toast(R.string.toast_write_pad_ota_no_space)
                        requireActivity().finish()
                    }

                }
            }
        }
    }
}