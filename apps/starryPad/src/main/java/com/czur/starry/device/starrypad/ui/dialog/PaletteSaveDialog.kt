package com.czur.starry.device.starrypad.ui.dialog

import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.starrypad.databinding.DialogSaveWarnBinding

/**
 * Created by 陈丰尧 on 2024/1/4
 */
class PaletteSaveDialog(
    val clickListener: (type: FloatingBtnType, dialog: PaletteSaveDialog) -> Unit,
) : CZVBFloatingFragment<DialogSaveWarnBinding>() {
    override fun FloatingFragmentParams.initFloatingParams() {
        outSideDismiss = false  // 点击外部不消失
    }

    override fun DialogSaveWarnBinding.initBindingViews() {
        noBtn.setOnClickListener {
            clickListener(FloatingBtnType.NEGATIVE, this@PaletteSaveDialog)
        }
        yesBtn.setOnClickListener {
            clickListener(FloatingBtnType.POSITIVE, this@PaletteSaveDialog)
        }
    }
}