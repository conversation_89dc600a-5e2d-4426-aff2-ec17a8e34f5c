package com.czur.starry.device.starrypad.ui.aty

import android.os.Bundle
import androidx.activity.viewModels
import androidx.fragment.app.commit
import com.czur.czurutils.extension.platform.startService
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.databinding.ActivityMainBinding
import com.czur.starry.device.starrypad.hardware.WritePadService
import com.czur.starry.device.starrypad.ui.fragment.EmptyFragment
import com.czur.starry.device.starrypad.ui.fragment.PaintDataFragment
import com.czur.starry.device.starrypad.ui.fragment.TopStatusBarFragment
import com.czur.starry.device.starrypad.vm.PaintDataViewModel
import com.czur.starry.device.starrypadlib.StarryPadPaintHandler

/**
 * Created by 陈丰尧 on 2022/4/20
 */
class MainActivity : CZViewBindingAty<ActivityMainBinding>() {
    companion object {
        private const val TAG = "MainActivity"
    }

    private val paintDataVM: PaintDataViewModel by viewModels()

    override fun ActivityMainBinding.initBindingViews() {
        supportFragmentManager.commit {
            replace(R.id.topStatusBarContainer, TopStatusBarFragment())
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)


        repeatCollectOnCreate(paintDataVM.hasPaintDataLive) { hasData ->
            logTagV(TAG, "画板数据是否存在: $hasData")
            if (hasData) {
                supportFragmentManager.commit(true) {
                    replace(R.id.mainContentContainer, PaintDataFragment())
                }
            } else {
                supportFragmentManager.commit(true) {
                    replace(R.id.mainContentContainer, EmptyFragment())
                }
            }
        }

        repeatCollectOnResume(paintDataVM.displayModeFlow) {
            when (it) {
                PaintDataViewModel.DisplayMode.BROWSE -> binding.topStatusBarContainer.show()
                PaintDataViewModel.DisplayMode.DEL,
                PaintDataViewModel.DisplayMode.SHARE,
                PaintDataViewModel.DisplayMode.SAVE_LOCAL -> binding.topStatusBarContainer.gone()
            }
        }
        

        startService<WritePadService>()

        // 清空未读画板数据
        repeatOnResume {
            logTagV(TAG, "清空未读画板数据")
            StarryPadPaintHandler.newPaintFileName = ""
        }
    }

    override fun onPause() {
        StarryPadPaintHandler.newPaintFileName = ""
        super.onPause()
    }

}