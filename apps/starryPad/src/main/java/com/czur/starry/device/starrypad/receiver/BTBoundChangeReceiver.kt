package com.czur.starry.device.starrypad.receiver

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.onSuspendMainScope
import com.czur.starry.device.bluetoothlib.util.unpair
import com.czur.starry.device.starrypad.common.WP_NAME_PREFIX
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager

/**
 * Created by 陈丰尧 on 2024/2/29
 * 蓝牙绑定状态改变广播接收器
 */
private const val TAG = "BTBoundChangeReceiver"

@SuppressLint("MissingPermission")
class BTBoundChangeReceiver : BroadcastReceiver() {
    var onUserDeUnbindListener: ((devName: BluetoothDevice) -> Unit)? = null
    var onBondStateChangedListener: ((device: BluetoothDevice, bondState: Int) -> Unit)? = null

    override fun onReceive(context: Context?, intent: Intent?) {
        intent?.let {
            when (it.action) {
                BluetoothDevice.ACTION_BOND_STATE_CHANGED -> {
                    val device =
                        it.getParcelableExtra<BluetoothDevice>(BluetoothDevice.EXTRA_DEVICE)
                    val deviceName = device?.name ?: ""
                    if (deviceName.startsWith(WP_NAME_PREFIX)) {
                        // 是手写板设备
                        val bondState = it.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, -1)
                        val preBondState =
                            it.getIntExtra(BluetoothDevice.EXTRA_PREVIOUS_BOND_STATE, -1)

                        // 通知状态变化监听器
                        device?.let { dev ->
                            onBondStateChangedListener?.invoke(dev, bondState)
                        }

                        if (preBondState == BluetoothDevice.BOND_BONDED && bondState == BluetoothDevice.BOND_NONE) {
                            logTagV(TAG, "接收系统广播:解绑设备: $deviceName")
                            device?.let {dev ->
                                onUserDeUnbindListener?.invoke(dev)
                            }
                        } else if (bondState == BluetoothDevice.BOND_BONDED) {
                            logTagW(TAG, "接收系统广播:绑定设备: $deviceName")
                            onSuspendMainScope {
                                if (!WPDeviceInfoManager.isLegalDevice(deviceName)) {
                                    logTagW(TAG, "非法设备, 解绑: $deviceName")
                                    device?.unpair()
                                }
                            }
                        }
                    }

                }
            }
        }
    }

}