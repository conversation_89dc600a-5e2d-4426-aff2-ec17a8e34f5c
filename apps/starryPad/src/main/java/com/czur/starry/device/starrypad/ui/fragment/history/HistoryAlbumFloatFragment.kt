package com.czur.starry.device.starrypad.ui.fragment.history

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.signature.ObjectKey
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.utils.addItemDecorationDrawable
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.toastFail
import com.czur.starry.device.baselib.utils.toastSuccess
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.common.WPFuncOption
import com.czur.starry.device.starrypad.databinding.FloatFragmentHistoryAlbumBinding
import com.czur.starry.device.starrypad.db.entity.AlbumWithPaintList
import com.czur.starry.device.starrypad.ui.adapter.HistoryPaintAdapter
import com.czur.starry.device.starrypad.ui.dialog.DelWarnDialog
import com.czur.starry.device.starrypad.ui.dialog.SaveLocalProcessDialog
import com.czur.starry.device.starrypad.ui.window.GlobalMarkService
import com.czur.starry.device.starrypad.ui.window.PaletteService
import com.czur.starry.device.starrypad.ui.window.ShareAlertWindow
import com.czur.starry.device.starrypad.vm.HistoryAlbumViewModel
import com.czur.starry.writepadlib.proto.WPTransferData.Mode.*

/**
 * Created by 陈丰尧 on 2023/1/12
 * 历史画册页面
 */
class HistoryAlbumFloatFragment(
    private val albumWithPaintList: AlbumWithPaintList
) : CZVBFloatingFragment<FloatFragmentHistoryAlbumBinding>() {
    companion object {
        private const val TAG = "HistoryAlbumFloatFragment"
    }

    private val historyAlbumVM: HistoryAlbumViewModel by viewModels()
    private val adapter = HistoryPaintAdapter()

    override fun FloatingFragmentParams.initFloatingParams() {
        viewLifecycleObserver = object : AutoRemoveLifecycleObserver {
            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                activity?.launch {
                    // 更新打开时间
                    if (historyAlbumVM.hasDel) {
                        historyAlbumVM.updateOpenTime(albumWithPaintList.album)
                    }
                }
            }
        }
    }

    override fun FloatFragmentHistoryAlbumBinding.initBindingViews() {
        changeUIByOption()  // 根据配置改变UI

        albumTitleTv.text = albumWithPaintList.album.createTimeStr

        albumPaintListRv.adapter = adapter

        // 添加间距
        albumPaintListRv.addItemDecorationDrawable(
            RecyclerView.HORIZONTAL,
            R.drawable.decoration_history_paint
        )

        albumPaintListRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            historyAlbumVM.currentIndex = pos
            true
        }
        albumPaintListRv.closeDefChangeAnimations()

        // 关闭按钮
        historyCloseIv.setOnClickListener {
            dismiss()
        }
        // 删除按钮
        historyDelIv.setOnClickListener {
            DelWarnDialog {
                launch {
                    it.dismiss()
                    historyAlbumVM.delCurrent()
                }
            }.show()
        }

        historyShareIv.setOnClickListener {
            logTagV(TAG, "分享单页笔记")
            val paint = historyAlbumVM.currentPaintEntity ?: return@setOnClickListener

            ShareAlertWindow.sharePaint(requireContext(), listOf(paint))
        }

        historySaveLocalIv.setOnClickListener {
            logTagV(TAG, "保存单页笔记")
            val paint = historyAlbumVM.currentPaintEntity ?: return@setOnClickListener
            SaveLocalProcessDialog(listOf(paint)) { dialog, error ->
                dialog.dismiss()
                if (error != null) {
                    toastFail()
                } else {
                    toastSuccess()
                }
            }.show()
        }

        historyEditIv.setOnClickListener {
            logTagV(TAG, "编辑单页笔记")
            launch {
                /// 这部分为原有逻辑
                val albumID = albumWithPaintList.album.albumId
                val currentIndex = historyAlbumVM.currentIndex
                val mode = albumWithPaintList.albumMode
                logTagD(TAG, "albumID:$albumID,currentIndex:$currentIndex")
                when (mode) {
                    MODE_PALETTE -> PaletteService.startHistory(
                        requireContext(),
                        albumID,
                        currentIndex
                    )

                    MODE_MARK -> GlobalMarkService.startHistory(
                        requireContext(),
                        albumID,
                        currentIndex
                    )

                    else -> {
                        logTagW(TAG, "错误的模式,无法启动")
                    }
                }
                dismiss()
            }

        }
    }

    private fun changeUIByOption() {
        logTagV(TAG, "changeUIByOption")
        binding.historyEditIv.gone(!WPFuncOption.FUNC_HISTORY_EDIT_ENABLE)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        launch {
            // 更新打开时间
            historyAlbumVM.updateOpenTime(albumWithPaintList.album)
        }

        historyAlbumVM.initAlbumData(albumWithPaintList)

        // 笔记数量
        historyAlbumVM.paintCountLive.observe(this) { countText ->
            binding.albumPaintCountTv.text = countText
        }

        historyAlbumVM.currentAlbumLive.observe(this) {
            if (it == null) {
                logTagD(TAG, "相册整体都被删除了")
                dismiss()
            }
        }

        historyAlbumVM.currentPaintPath.observe(this) {
            if (it.isNotEmpty()) {
                // 加载对应的图片
                logTagV(TAG, "加载图片:$it")
                Glide.with(this)
                    .load(it)
                    .signature(
                        ObjectKey("${it}${historyAlbumVM.modifyTime}"),
                    )
                    .into(binding.currentShowIv)
            }
        }

        historyAlbumVM.historyPaintListLive.observe(this) {
            adapter.setData(it)
        }

    }
}