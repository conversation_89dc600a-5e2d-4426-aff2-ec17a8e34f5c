package com.czur.starry.device.starrypad.ui.settings.add

import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.starrypad.R

/**
 * Created by 陈丰尧 on 2024/2/26
 */

data class FoundWPDevice(
    val address: String,
    val name: String,
    val sel: <PERSON><PERSON><PERSON>,
)

class FoundWPAdapter : BaseDifferAdapter<FoundWPDevice>() {
    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: FoundWPDevice) {
        holder.setText(itemData.name, R.id.wpFoundDeviceNameTv)
        holder.visible(itemData.sel, R.id.wpFoundDevSelectedIv)
    }

    override fun areItemsTheSame(oldItem: FoundWPDevice, newItem: FoundWPDevice): Boolean {
        return oldItem.address == newItem.address
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_found_wp_dev, parent)
    }
}