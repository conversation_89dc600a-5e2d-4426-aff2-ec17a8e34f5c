package com.czur.starry.device.starrypad.ui.dialog

import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.databinding.DialogDelWarnBinding

/**
 * Created by 陈丰尧 on 2022/5/13
 */
class DelWarnDialog(
    val delListener: (dialog: DelWarnDialog) -> Unit,
) : CZVBFloatingFragment<DialogDelWarnBinding>() {

    override fun DialogDelWarnBinding.initBindingViews() {
        delBtn.setOnClickListener {
            delListener(this@DelWarnDialog)
        }

        cancelBtn.setOnClickListener {
            dismiss()
        }
    }
}