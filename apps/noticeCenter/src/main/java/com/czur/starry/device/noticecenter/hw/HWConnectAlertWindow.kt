package com.czur.starry.device.noticecenter.hw

import android.content.Intent
import android.util.TypedValue
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.AlertWindowService
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.getScreenHeight
import com.czur.starry.device.baselib.utils.getScreenWidth
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.noticecenter.R
import com.czur.starry.device.noticelib.hwconn.HwConnHelper
import com.czur.starry.device.noticelib.hwconn.HwConnHelper.KEY_HW_CONN_TYPE
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import androidx.constraintlayout.widget.ConstraintLayout
import com.bumptech.glide.load.resource.gif.GifDrawable
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.Q1Series
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.common.hw.StudioSeries
import kotlin.reflect.KClass

/**
 * Created by 陈丰尧 on 2023/11/24
 */
private const val SHOW_TIME = 5 * ONE_SECOND    // 显示5s
private const val TAG = "HWConnectAlertWindow"

class HWConnectAlertWindow : AlertWindowService() {
    override val layoutId: Int
        get() = R.layout.alert_window_hw_connect
    override val windowWidthParam: Int
        get() = getScreenWidth()
    override val windowHeightParam: Int
        get() = getScreenHeight()

    private val connIv: ImageView by ViewFinder(R.id.hwConnIv)
    private val hwConnTitleTv: TextView by ViewFinder(R.id.hwConnTitleTv)
    private val hwConnStateTv: TextView by ViewFinder(R.id.hwConnStateTv)
    private val hoverView: View by ViewFinder(R.id.hoverView)
    private var delayStopJob: Job? = null

    override fun View.initViews() {
        logTagV(TAG, "注册hover监听")
        hoverView.setOnHoverListener { _, event ->
            if (event.action == MotionEvent.ACTION_HOVER_MOVE) {
                logTagV(TAG, "鼠标移动, 消去弹窗")
                stopSelf()
            }
            true
        }
    }

    override fun onDataRefresh(intent: Intent?) {
        super.onDataRefresh(intent)
        delayStopJob?.cancel()  // 有新的数据来了, 取消之前的定时关闭任务, 并重新开始计时
        delayStopJob = launch {
            logTagV(TAG, "5s后关闭")
            delay(SHOW_TIME)
            logTagV(TAG, "已经显示5s, stopSelf")
            stopSelf()
        }
        val typeName = intent?.getStringExtra(KEY_HW_CONN_TYPE)
        logTagV(TAG, "onDataRefresh: $typeName")
        if (typeName == null) {
            logTagW(TAG, "typeName is null,自动销毁")
            stopSelf()
            return
        }
        val type = try {
            HwConnHelper.HwConnType.valueOf(typeName)
        } catch (tr: Throwable) {
            logTagW(TAG, "typeName($typeName) is not a valid HwConnType,自动销毁")
            stopSelf()
            return
        }

        when (type) {
            HwConnHelper.HwConnType.TOUCH_BOARD_V1,
            HwConnHelper.HwConnType.TOUCH_BOARD_V2 -> {
                hwConnTitleTv.text = getString(R.string.str_hw_name_touch_board)
                Glide.with(this)
                    .load(R.drawable.hw_touch_v2)
                    .override(380)
                    .into(connIv)
            }

            HwConnHelper.HwConnType.BT_KEYBOARD -> {
                hwConnTitleTv.text = getString(R.string.str_hw_name_keyboard)
                Glide.with(this)
                    .load(R.drawable.hw_keyboard)
                    .override(440)
                    .into(connIv)
            }

            HwConnHelper.HwConnType.CLICK_DROP_TYPE_C -> {
                hwConnTitleTv.text = getString(R.string.str_hw_name_click_drop)
                hwConnStateTv.text = getString(R.string.str_hw_paired_success)
                Glide.with(this)
                    .load(R.drawable.hw_click_drop_type_c)
                    .override(380)
                    .into(connIv)
            }

            HwConnHelper.HwConnType.CLICK_DROP_TYPE_A -> {
                hwConnTitleTv.text = getString(R.string.str_hw_name_click_drop)
                hwConnStateTv.text = getString(R.string.str_hw_paired_success)
                Glide.with(this)
                    .load(R.drawable.hw_click_drop_type_a)
                    .override(380)
                    .into(connIv)
            }

            HwConnHelper.HwConnType.SCREEN_SAVER -> {
                hwConnTitleTv.text = getString(R.string.str_hw_name_set_successfully)
                hwConnStateTv.text = getString(R.string.str_hw_name_set_content)
                hwConnStateTv.setTextColor(getColor(R.color.black))
                hwConnStateTv.setTextSize(TypedValue.COMPLEX_UNIT_PX, 24f)

                val layoutParams1: ConstraintLayout.LayoutParams =
                    connIv.layoutParams as ConstraintLayout.LayoutParams
                layoutParams1.topToTop = ConstraintLayout.LayoutParams.UNSET
                connIv.layoutParams = layoutParams1

                val layoutParams2: ConstraintLayout.LayoutParams =
                    hwConnStateTv.layoutParams as ConstraintLayout.LayoutParams
                layoutParams2.bottomMargin = ConstraintLayout.LayoutParams.UNSET
                layoutParams2.bottomToBottom = ConstraintLayout.LayoutParams.UNSET
                layoutParams2.topToBottom = hwConnTitleTv.id
                layoutParams2.topMargin = 25
                hwConnStateTv.layoutParams = layoutParams2
                when (Constants.starryHWInfo.series) {
                    StudioSeries -> {
                        if (Constants.starryHWInfo.hasTouchScreen) {
                            Glide.with(this)
                                .load(R.drawable.img_touchboard_studio_sp)
                                .override(650, 550)
                                .into(connIv)
                        } else {
                            Glide.with(this)
                                .load(R.drawable.img_touchboard_studio)
                                .override(650, 550)
                                .into(connIv)
                        }
                    }

                    Q2Series -> {
                        Glide.with(this)
                            .load(R.drawable.img_touchboard_q2)
                            .override(650, 550)
                            .into(connIv)
                    }

                    Q1Series -> {
                        Glide.with(this)
                            .load(R.drawable.img_touchboard)
                            .override(440)
                            .into(connIv)
                    }

                    else -> {
                        logTagW(TAG, "没有对应的动画")
                        stopSelf()
                    }
                }

            }

            HwConnHelper.HwConnType.WRITE_PAD -> {
                hwConnTitleTv.text = getString(R.string.str_hw_name_write_pad)
                Glide.with(this)
                    .load(R.drawable.hw_writepad)
                    .override(380)
                    .into(connIv)
            }
        }
    }
}