package com.czur.starry.device.settings.ui.projector.audio

import android.app.Application
import android.media.AudioDeviceCallback
import android.media.AudioDeviceInfo
import android.media.AudioManager
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.settings.utils.getAudioDeviceInfoDisplayName
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2025/4/7
 */
private const val TAG = "SpeakerAndMicChooseViewModel"

class SpeakerAndMicChooseViewModel(application: Application) : AndroidViewModel(application) {

    // ================================ 系统服务 ================================

    private val audioManager by lazy {
        application.getSystemService(Application.AUDIO_SERVICE) as AudioManager
    }

    private val systemManager: SystemManagerProxy by lazy {
        SystemManagerProxy()
    }

    // ================================ 私有状态Flow ================================

    /**
     * 原始音频设备列表，只包含在线设备
     */
    private val _audioDeviceListFlow = MutableStateFlow<List<CZAudioDeviceInfo>>(emptyList())

    /**
     * 当前选中的扬声器设备类型
     * -1 表示自动选择
     */
    private val _currentSelSpeakerTypeFlow = MutableStateFlow(-1)

    /**
     * 当前选中的麦克风设备类型
     * -1 表示自动选择
     */
    private val _currentSelMicTypeFlow = MutableStateFlow(-1)

    // ================================ 公开的状态Flow ================================

    /**
     * 麦克风是否为自动选择模式
     */
    val autoSelMicFlow = _currentSelMicTypeFlow.map {
        it == -1
    }.stateIn(
        viewModelScope,
        initialValue = true,
        started = SharingStarted.Lazily
    )

    /**
     * 扬声器是否为自动选择模式
     */
    val autoSelSpeakerFlow = _currentSelSpeakerTypeFlow.map {
        it == -1
    }.stateIn(
        viewModelScope,
        initialValue = true,
        started = SharingStarted.Lazily
    )

    // ================================ 私有工具方法 ================================

    /**
     * 检查设备类型是否匹配
     */
    private fun isDeviceTypeMatched(deviceType: Int, targetType: Int): Boolean {
        return deviceType == targetType
    }

    /**
     * 创建音频设备Flow的通用方法
     * @param isSpeaker true为扬声器，false为麦克风
     * @param selectedTypeFlow 当前选中的设备类型Flow
     * @param deviceFilter 设备过滤条件
     * @return 包含在线设备和必要离线设备的StateFlow
     */
    private fun createAudioDeviceFlow(
        isSpeaker: Boolean,
        selectedTypeFlow: MutableStateFlow<Int>,
        deviceFilter: (CZAudioDeviceInfo) -> Boolean
    ) = combine(
        _audioDeviceListFlow,
        selectedTypeFlow
    ) { list, selectedType ->
        val devices = list.filter(deviceFilter).toMutableList()

        // 如果有选中的设备类型且不是自动选择(-1)，检查是否需要添加断连设备
        if (selectedType != -1) {
            val hasSelectedDevice = devices.any { device ->
                isDeviceTypeMatched(device.type, selectedType) && device.isOnLine
            }
            if (!hasSelectedDevice) {
                // 添加离线设备
                devices.add(CZAudioDeviceInfo().apply {
                    setFocusInfo(selectedType, isSpeaker)
                })
            }
        }
        devices
    }.stateIn(
        viewModelScope,
        initialValue = emptyList(),
        started = SharingStarted.Lazily
    )

    /**
     * 创建选中索引Flow的通用方法
     * @param selectedTypeFlow 当前选中的设备类型Flow
     * @param deviceFlow 设备列表Flow
     * @param deviceTypeName 设备类型名称，用于日志
     * @param allowAutoSelect 是否允许自动选择（麦克风允许，扬声器不允许）
     * @return 当前选中设备在列表中的索引StateFlow
     */
    private fun createSelectedIndexFlow(
        selectedTypeFlow: MutableStateFlow<Int>,
        deviceFlow: StateFlow<List<CZAudioDeviceInfo>>,
        deviceTypeName: String,
        allowAutoSelect: Boolean = false
    ) = combine(selectedTypeFlow, deviceFlow) { deviceType, devices ->
        if (allowAutoSelect && deviceType == -1) {
            // 系统自动选择（仅麦克风支持）
            return@combine -1
        }

        if (devices.isEmpty()) {
            return@combine -1
        }

        var index = devices.indexOfFirst { device ->
            isDeviceTypeMatched(device.type, deviceType) && device.isOnLine
        }

        if (index == -1) {
            logTagW(
                TAG,
                "没有找到对应的${deviceTypeName}类型:$deviceType, 可用设备: ${devices.map { it.displayName + "(${it.type})" }}"
            )
            index = devices.indexOfFirst {
                it.type == -1   // 默认使用自动选择
            }
        }
        index.coerceAtLeast(0)
    }.stateIn(
        viewModelScope,
        initialValue = 0,
        started = SharingStarted.Lazily
    )

    /**
     * 扬声器设备列表Flow
     * 包含在线设备和必要的离线设备
     */
    val speakerDeviceFlow = createAudioDeviceFlow(
        isSpeaker = true,
        selectedTypeFlow = _currentSelSpeakerTypeFlow,
        deviceFilter = { it.isSink }
    )

    /**
     * 麦克风设备列表Flow
     * 包含在线设备和必要的离线设备，排除蓝牙A2DP设备
     */
    val micDeviceFlow = createAudioDeviceFlow(
        isSpeaker = false,
        selectedTypeFlow = _currentSelMicTypeFlow,
        deviceFilter = { !it.isSink && it.type != AudioDeviceInfo.TYPE_BLUETOOTH_A2DP }
    )

    /**
     * 当前选中的扬声器在列表中的索引
     */
    val selSpeakerIndexFlow = createSelectedIndexFlow(
        selectedTypeFlow = _currentSelSpeakerTypeFlow,
        deviceFlow = speakerDeviceFlow,
        deviceTypeName = "Speaker",
        allowAutoSelect = false
    )

    /**
     * 当前选中的麦克风在列表中的索引
     * 支持自动选择模式（返回-1）
     */
    val selMicIndexFlow = createSelectedIndexFlow(
        selectedTypeFlow = _currentSelMicTypeFlow,
        deviceFlow = micDeviceFlow,
        deviceTypeName = "Mic",
        allowAutoSelect = true
    )

    // ================================ 音频设备监听 ================================

    /**
     * 音频设备变化监听器
     * 当设备插拔时自动更新设备列表
     */
    private val audioDeviceCb = object : AudioDeviceCallback() {
        override fun onAudioDevicesAdded(addedDevices: Array<out AudioDeviceInfo?>?) {
            super.onAudioDevicesAdded(addedDevices)
            launch {
                updateDeviceList()
            }
        }

        override fun onAudioDevicesRemoved(removedDevices: Array<out AudioDeviceInfo?>?) {
            super.onAudioDevicesRemoved(removedDevices)
            launch {
                updateDeviceList()
            }
        }
    }

    // ================================ 初始化 ================================

    init {
        // 注册音频设备变化监听
        launch {
            audioManager.registerAudioDeviceCallback(audioDeviceCb, null)
        }

        // 初始化当前选中的设备类型
        launch {
            refreshCurrentSelectedDeviceTypes()
        }
    }

    // ================================ 公共方法 ================================

    /**
     * 更新选中的音频设备
     * @param device 要选中的设备
     * @param isSpeaker 是否为扬声器设备，默认根据device.isSink判断
     */
    fun updateSelDevice(
        device: CZAudioDeviceInfo,
        isSpeaker: Boolean = device.isSink
    ) {
        logTagD(
            TAG,
            "updateSelDevice:${device.displayName} ${device.isSink}"
        )
        if (!device.isOnLine) {
            logTagV(TAG, "设备已经离线, 不能主动指定")
            return
        }

        val res = systemManager.setPreferredAudioDevice(device.type, isSpeaker)
        if (res) {
            logTagD(TAG, "设置成功:${device.displayName}, 类型:${device.type}")
            // 系统会自动写入 persist.czur.outtype 和 persist.czur.intype
            // APP只需要更新本地的状态用于UI显示
            if (device.isSink) {
                _currentSelSpeakerTypeFlow.value = device.type
            } else {
                _currentSelMicTypeFlow.value = device.type
            }
        } else {
            logTagW(TAG, "设置失败")
        }
    }

    /**
     * 更新音频设备列表
     * 只负责获取和更新原始的在线设备数据，断连设备的添加由各自的Flow处理
     */
    suspend fun updateDeviceList() = withContext(Dispatchers.Default) {
        logTagV(TAG, "更新音频设备列表")
        val audioDevices =
            audioManager.getDevices(AudioManager.GET_DEVICES_OUTPUTS or AudioManager.GET_DEVICES_INPUTS)
        logTagV(TAG, "┌-- audioDevice --┐")

        // 过滤掉不需要的设备类型
        val currentOnLineAudioDevice = audioDevices.filter {
            // 排除远程提交和蓝牙SCO设备
            if (it.type == AudioDeviceInfo.TYPE_REMOTE_SUBMIX
                || it.type == AudioDeviceInfo.TYPE_BLUETOOTH_SCO
            ) {
                return@filter false
            }
            // 排除输入端的HDMI设备（这些通常不是真正的麦克风）
            if (!it.isSink && (it.type == AudioDeviceInfo.TYPE_HDMI ||
                        it.type == AudioDeviceInfo.TYPE_HDMI_ARC ||
                        it.type == AudioDeviceInfo.TYPE_HDMI_ARC ||
                        it.type == AudioDeviceInfo.TYPE_HDMI_EARC)
            ) {
                return@filter false
            }
            logTagV(TAG, "audioDevice:${getAudioDeviceInfoDisplayName(it.type)}")
            true
        }
        logTagV(TAG, "└-- audioDevice --┘")

        // 只保存原始的在线设备数据，断连设备的添加逻辑移到各自的Flow中处理
        val deviceList = currentOnLineAudioDevice.map {
            CZAudioDeviceInfo().apply {
                audioDeviceInfo = it
            }
        }
        _audioDeviceListFlow.value = deviceList

        // 设备列表更新后，重新读取系统属性以获取最新的选中设备
        launch {
            refreshCurrentSelectedDeviceTypes()
        }
    }

    // ================================ 私有方法 ================================

    /**
     * 刷新当前选中的设备类型
     * 从系统属性重新读取扬声器和麦克风的设备类型
     */
    private suspend fun refreshCurrentSelectedDeviceTypes() {
        _currentSelSpeakerTypeFlow.value = SettingUtil.CameraAndMicSetting.getDefaultSpeakerType()
        _currentSelMicTypeFlow.value = SettingUtil.CameraAndMicSetting.getDefaultMicType()
    }

    // ================================ 生命周期 ================================

    override fun onCleared() {
        audioManager.unregisterAudioDeviceCallback(audioDeviceCb)
        super.onCleared()
    }
}