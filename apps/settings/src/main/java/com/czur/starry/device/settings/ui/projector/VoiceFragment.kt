package com.czur.starry.device.settings.ui.projector

import android.os.Bundle
import android.view.KeyEvent
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.listener.KeyDownListener
import com.czur.starry.device.baselib.base.listener.KeyUpListener
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentVoiceBinding

/**
 * Created by 陈丰尧 on 1/28/21
 */
@Deprecated("Use SpeakerFragment instead")
class VoiceFragment : BaseBindingMenuFragment<FragmentVoiceBinding>(), KeyDownListener, KeyUpListener {
    companion object {
        private const val TAG = "VoiceFragment"
    }

    private val voiceVM: VoiceVM by viewModels()

    override fun FragmentVoiceBinding.initBindingViews() {
        // 恢复默认
        setVolumeDefBtn.setOnClickListener {
            voiceVM.resetToDef()
        }

        // 设置seekBar
        callSeekBar.max = SHOW_MAX_VOLUME
        callSeekBar.min = VOLUME_CALL_MIN

        mediaSeekBar.max = SHOW_MAX_VOLUME
        mediaSeekBar.min = VOLUME_MEDIA_MIN

        callSeekBar.onProgressChangerListener = {progress, _ ->
            callValueTv.text = progress.toString()
        }

        callSeekBar.onProgressChangeCompletedListener = {progress, fromUser ->
            if (fromUser) {
                voiceVM.resetPlay()
                voiceVM.modifyVolume(progress, VoiceVM.AdjustStream.CALL)
            }
        }

        mediaSeekBar.onProgressChangerListener = {progress, _ ->
            mediaValueTv.text = progress.toString()
        }
        mediaSeekBar.onProgressChangeCompletedListener = {progress, fromUser ->
            if (fromUser) {
                voiceVM.resetPlay()
                voiceVM.modifyVolume(progress, VoiceVM.AdjustStream.MEDIA)
            }
        }

        voiceVM.callVolumeLive.observe(viewLifecycleOwner) {
            callValueTv.text = it.toString()
            callSeekBar.progress = it
        }

        voiceVM.mediaVolumeLive.observe(viewLifecycleOwner) {
            mediaValueTv.text = it.toString()
            mediaSeekBar.progress = it
        }

        voiceVM.resetEnable.observe(viewLifecycleOwner) {
            setVolumeDefBtn.isEnabled = it
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        voiceVM.refreshVolumes()
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return when (keyCode) {
            KeyEvent.KEYCODE_VOLUME_UP -> {
                logTagV(TAG, "按下音量上键")
                voiceVM.modifyingVolume(VoiceVM.AdjustMode.VOL_UP, VoiceVM.AdjustStream.CALL)
                true
            }
            KeyEvent.KEYCODE_VOLUME_DOWN -> {
                logTagV(TAG, "按下音量下键")
                voiceVM.modifyingVolume(VoiceVM.AdjustMode.VOL_DOWN, VoiceVM.AdjustStream.CALL)
                true
            }
            KeyEvent.KEYCODE_VOLUME_MUTE -> {
                logTagV(TAG, "按下音量静音键")
                voiceVM.modifyingVolume(VoiceVM.AdjustMode.MUTE, VoiceVM.AdjustStream.CALL)
                true
            }
            else -> false

        }
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        return when (keyCode) {
            KeyEvent.KEYCODE_VOLUME_UP, KeyEvent.KEYCODE_VOLUME_DOWN,KeyEvent.KEYCODE_VOLUME_MUTE -> {
                logTagD(TAG, "抬起音量键")
                voiceVM.resetPlay()
                true
            }
            else -> false

        }
    }
}