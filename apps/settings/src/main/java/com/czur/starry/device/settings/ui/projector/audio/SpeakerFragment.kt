package com.czur.starry.device.settings.ui.projector.audio

import android.os.Bundle
import android.view.KeyEvent
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.listener.KeyDownListener
import com.czur.starry.device.baselib.base.listener.KeyUpListener
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentSpeakerBinding
import com.czur.starry.device.settings.ui.projector.SHOW_MAX_VOLUME
import com.czur.starry.device.settings.ui.projector.VOLUME_CALL_MIN
import com.czur.starry.device.settings.ui.projector.VOLUME_MEDIA_MIN
import com.czur.starry.device.settings.ui.projector.VoiceVM
import com.czur.uilib.choose.CZCheckBox
import com.czur.uilib.extension.rv.addCornerRadius

/**
 * Created by 陈丰尧 on 2025/4/10
 */
private const val TAG = "SpeakerFragment"
class SpeakerFragment : BaseBindingMenuFragment<FragmentSpeakerBinding>(), KeyDownListener, KeyUpListener  {
    private val speakerAndMicChooseViewModel: SpeakerAndMicChooseViewModel by activityViewModels()
    private val audioDeviceInfoAdapter = AudioDeviceInfoAdapter()
    private val voiceVM: VoiceVM by viewModels()


    override fun FragmentSpeakerBinding.initBindingViews() {
        systemAutoChooseCb.blockOperation = CZCheckBox.BlockUserUnCheckOperation
        manualChooseCb.blockOperation = CZCheckBox.BlockUserUnCheckOperation

        systemAutoChooseCb.setOnCheckedChangeListener { isOn, fromUser ->
            if (fromUser && isOn) {
                // 系统自动选择
                val autoSpeaker = CZAudioDeviceInfo().apply {
                    setFocusInfo(-1, true)
                }
                speakerAndMicChooseViewModel.updateSelDevice(autoSpeaker)
            }

            speakerRv.isEnabled = !isOn
            speakerRv.alpha = if (isOn) 0.5F else 1.0F
        }

        manualChooseCb.setOnCheckedChangeListener { isOn, fromUser ->
            if (fromUser && isOn) {
                val data = audioDeviceInfoAdapter.getData(0)
                speakerAndMicChooseViewModel.updateSelDevice(data)
            }
        }

        speakerRv.adapter = audioDeviceInfoAdapter
        speakerRv.addCornerRadius(10F, 0xFFF1F3FE.toInt())
        speakerRv.doOnItemClick { holder, view ->
            val position = holder.bindingAdapterPosition
            if (position != audioDeviceInfoAdapter.selItem && manualChooseCb.isChecked()) {
                val device = audioDeviceInfoAdapter.getData(position)
                speakerAndMicChooseViewModel.updateSelDevice(device)
            }
            true
        }

        // 设置seekBar
        callSeekBar.max = SHOW_MAX_VOLUME
        callSeekBar.min = 0  // 最小值设为0
        callSeekBar.operationMin = VOLUME_CALL_MIN  // 操作最小值设为1

        mediaSeekBar.max = SHOW_MAX_VOLUME
        mediaSeekBar.min = VOLUME_MEDIA_MIN

        callSeekBar.onProgressChangeCompletedListener = {progress, fromUser ->
            if (fromUser) {
                voiceVM.resetPlay()
                voiceVM.modifyVolume(progress, VoiceVM.AdjustStream.CALL)
            }
        }

        mediaSeekBar.onProgressChangeCompletedListener = {progress, fromUser ->
            if (fromUser) {
                voiceVM.resetPlay()
                voiceVM.modifyVolume(progress, VoiceVM.AdjustStream.MEDIA)
            }
        }

        voiceVM.callVolumeLive.observe(viewLifecycleOwner) {
            callSeekBar.progress = it
        }

        voiceVM.mediaVolumeLive.observe(viewLifecycleOwner) {
            mediaSeekBar.progress = it
        }

    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatOnResume {
            speakerAndMicChooseViewModel.updateDeviceList()
            voiceVM.refreshVolumes()
        }

        repeatCollectOnResume(speakerAndMicChooseViewModel.selSpeakerIndexFlow) {
            audioDeviceInfoAdapter.updateSelItem(it)
        }

        repeatCollectOnResume(speakerAndMicChooseViewModel.speakerDeviceFlow) {
            audioDeviceInfoAdapter.setData(it)
        }

        repeatCollectOnResume(speakerAndMicChooseViewModel.autoSelSpeakerFlow) {
            binding.systemAutoChooseCb.setChecked(it)
            binding.manualChooseCb.setChecked(!it)
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return when (keyCode) {
            KeyEvent.KEYCODE_VOLUME_UP -> {
                logTagV(TAG, "按下音量上键")
                voiceVM.modifyingVolume(VoiceVM.AdjustMode.VOL_UP, VoiceVM.AdjustStream.CALL)
                true
            }
            KeyEvent.KEYCODE_VOLUME_DOWN -> {
                logTagV(TAG, "按下音量下键")
                voiceVM.modifyingVolume(VoiceVM.AdjustMode.VOL_DOWN, VoiceVM.AdjustStream.CALL)
                true
            }
            KeyEvent.KEYCODE_VOLUME_MUTE -> {
                logTagV(TAG, "按下音量静音键")
                voiceVM.modifyingVolume(VoiceVM.AdjustMode.MUTE, VoiceVM.AdjustStream.CALL)
                true
            }
            else -> false

        }
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent?): Boolean {
        return when (keyCode) {
            KeyEvent.KEYCODE_VOLUME_UP, KeyEvent.KEYCODE_VOLUME_DOWN,KeyEvent.KEYCODE_VOLUME_MUTE -> {
                logTagD(TAG, "抬起音量键")
                voiceVM.resetPlay()
                true
            }
            else -> false

        }
    }
}