package com.czur.starry.device.settings.ui.projector.cec

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentCecBinding
import com.czur.uilib.CZSwitch

/**
 * Created by 陈丰尧 on 2025/3/7
 */
private const val TAG = "CECFragment"

class CECFragment : BaseBindingMenuFragment<FragmentCecBinding>() {
    private val cecViewModel: CECViewModel by viewModels()

    override fun FragmentCecBinding.initBindingViews() {
        cecMasterSwitch.addListener(cecViewModel::setCECEnable)
        screenFollowDeviceSwitch.addListener(cecViewModel::setScreenFollowDevice)
        deviceFollowScreenSwitch.addListener(cecViewModel::setDeviceFollowScreen)

        cecHelperIv.setOnDebounceClickListener {
            CECHelperFloating().show()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatCollectOnResume(cecViewModel.cecEnableFlow) {
            binding.cecMasterSwitch.setSwitchOn(it)
            binding.screenFollowDeviceSwitch.isEnabled = it
            binding.deviceFollowScreenSwitch.isEnabled = it
        }

        repeatCollectOnResume(cecViewModel.screenFollowDeviceFlow) {
            binding.screenFollowDeviceSwitch.setSwitchOn(it)
        }

        repeatCollectOnResume(cecViewModel.deviceFollowScreenFlow) {
            binding.deviceFollowScreenSwitch.setSwitchOn(it)
        }
    }

    private fun CZSwitch.addListener(block: (Boolean) -> Unit) {
        setOnSwitchChangeListener { isOn, fromUser ->
            if (fromUser) {
                block(isOn)
            }
        }
    }
}