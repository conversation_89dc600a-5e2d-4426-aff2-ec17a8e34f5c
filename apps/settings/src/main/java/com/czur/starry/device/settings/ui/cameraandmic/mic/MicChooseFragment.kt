package com.czur.starry.device.settings.ui.cameraandmic.mic

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentMicChooseBinding
import com.czur.starry.device.settings.ui.projector.audio.AudioDeviceInfoAdapter
import com.czur.starry.device.settings.ui.projector.audio.CZAudioDeviceInfo
import com.czur.starry.device.settings.ui.projector.audio.SpeakerAndMicChooseViewModel
import com.czur.uilib.choose.CZCheckBox
import com.czur.uilib.extension.rv.addCornerRadius

/**
 * Created by 陈丰尧 on 2025/8/13
 */
class MicChooseFragment : BaseBindingMenuFragment<FragmentMicChooseBinding>() {
    private val speakerAndMicChooseViewModel: SpeakerAndMicChooseViewModel by activityViewModels()
    private val audioDeviceInfoAdapter = AudioDeviceInfoAdapter()

    override fun FragmentMicChooseBinding.initBindingViews() {
        systemAutoChooseCb.blockOperation = CZCheckBox.BlockUserUnCheckOperation
        manualChooseCb.blockOperation = CZCheckBox.BlockUserUnCheckOperation

        systemAutoChooseCb.setOnCheckedChangeListener { isOn, fromUser ->
            if (fromUser && isOn) {
                // 系统自动选择
                val autoMic = CZAudioDeviceInfo().apply {
                    setFocusInfo(-1, false)
                }
                speakerAndMicChooseViewModel.updateSelDevice(autoMic)
            }

            speakerRv.isEnabled = !isOn
            speakerRv.alpha = if (isOn) 0.5F else 1.0F
        }

        manualChooseCb.setOnCheckedChangeListener { isOn, fromUser ->
            if (fromUser && isOn) {
                val data = audioDeviceInfoAdapter.getData(0)
                speakerAndMicChooseViewModel.updateSelDevice(data)
            }
        }


        speakerRv.adapter = audioDeviceInfoAdapter
        speakerRv.addCornerRadius(10F, 0xFFF1F3FE.toInt())
        speakerRv.doOnItemClick { holder, view ->
            val position = holder.bindingAdapterPosition
            if (position != audioDeviceInfoAdapter.selItem && manualChooseCb.isChecked()) {
                val device = audioDeviceInfoAdapter.getData(position)
                speakerAndMicChooseViewModel.updateSelDevice(device)
            }
            true
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatOnResume {
            speakerAndMicChooseViewModel.updateDeviceList()
        }

        repeatCollectOnResume(speakerAndMicChooseViewModel.selMicIndexFlow) {
            audioDeviceInfoAdapter.updateSelItem(it)
        }

        repeatCollectOnResume(speakerAndMicChooseViewModel.micDeviceFlow) {
            audioDeviceInfoAdapter.setData(it)
        }

        repeatCollectOnResume(speakerAndMicChooseViewModel.autoSelMicFlow) {
            binding.systemAutoChooseCb.setChecked(it)
            binding.manualChooseCb.setChecked(!it)
        }
    }
}