<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/white"
    tools:ignore="PxUsage,RtlHardcoded">

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:constraint_referenced_ids="cecMasterSwitchBg,screenFollowDeviceBg,deviceFollowScreenBg,space,reminderBg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.uilib.bg.CZItemBgView
        android:id="@+id/cecMasterSwitchBg"
        android:layout_width="900px"
        android:layout_height="100px"
        app:czIgnoreHover="true"
        app:czItemPosition="top"
        app:czSelected="true" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:includeFontPadding="false"
        android:text="@string/cec_master_switch"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/cecMasterSwitchBg"
        app:layout_constraintLeft_toLeftOf="@id/cecMasterSwitchBg"
        app:layout_constraintTop_toTopOf="@id/cecMasterSwitchBg" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/cecMasterSwitch"
        style="@style/switch_cec_item"
        app:czSwitchBgOffColor="#5879FC"
        app:czSwitchBgOnColor="@color/white"
        app:czSwitchBorderColor="@color/white"
        app:czSwitchTextOffColor="@color/white"
        app:czSwitchTextOnColor="#5879FC"
        app:czSwitchThumbOffColor="@color/white"
        app:czSwitchThumbOnColor="#5879FC"
        app:layout_constraintBottom_toBottomOf="@id/cecMasterSwitchBg"
        app:layout_constraintRight_toRightOf="@id/cecMasterSwitchBg"
        app:layout_constraintTop_toTopOf="@id/cecMasterSwitchBg" />

    <com.czur.uilib.bg.CZItemBgView
        android:id="@+id/screenFollowDeviceBg"
        android:layout_width="900px"
        android:layout_height="128px"
        app:czIgnoreHover="true"
        app:czItemPosition="middle" />

    <TextView
        android:id="@+id/screenFollowDeviceStarryTv"
        style="@style/tv_cec_item_device"
        android:layout_marginLeft="30px"
        android:text="@string/cec_device_starry"
        app:layout_constraintBottom_toTopOf="@id/screenFollowDeviceHintTv"
        app:layout_constraintLeft_toLeftOf="@id/screenFollowDeviceBg"
        app:layout_constraintTop_toTopOf="@id/screenFollowDeviceBg"
        app:layout_constraintVertical_chainStyle="packed" />


    <include
        android:id="@+id/controlArrowStarry2Screen"
        layout="@layout/include_cec_control"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5px"
        app:layout_constraintBottom_toBottomOf="@id/screenFollowDeviceStarryTv"
        app:layout_constraintLeft_toRightOf="@id/screenFollowDeviceStarryTv" />


    <TextView
        style="@style/tv_cec_item_device"
        android:text="@string/cec_device_screen"
        app:layout_constraintBaseline_toBaselineOf="@id/screenFollowDeviceStarryTv"
        app:layout_constraintLeft_toRightOf="@id/controlArrowStarry2Screen" />

    <TextView
        android:id="@+id/screenFollowDeviceHintTv"
        style="@style/tv_cec_item_hint"
        android:text="@string/cec_screen_follow_device"
        app:layout_constraintBottom_toBottomOf="@id/screenFollowDeviceBg"
        app:layout_constraintLeft_toLeftOf="@id/screenFollowDeviceBg"
        app:layout_constraintTop_toBottomOf="@id/screenFollowDeviceStarryTv" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/screenFollowDeviceSwitch"
        style="@style/switch_cec_item"
        app:layout_constraintBottom_toBottomOf="@id/screenFollowDeviceBg"
        app:layout_constraintRight_toRightOf="@id/screenFollowDeviceBg"
        app:layout_constraintTop_toTopOf="@id/screenFollowDeviceBg" />

    <com.czur.uilib.bg.CZItemBgView
        android:id="@+id/deviceFollowScreenBg"
        android:layout_width="900px"
        android:layout_height="128px"
        app:czIgnoreHover="true"
        app:czItemPosition="bottom"/>

    <TextView
        android:id="@+id/cecDeviceScreenTv"
        style="@style/tv_cec_item_device"
        android:layout_marginLeft="30px"
        android:text="@string/cec_device_screen"
        app:layout_constraintBottom_toTopOf="@id/deviceFollowScreenHintTv"
        app:layout_constraintLeft_toLeftOf="@id/deviceFollowScreenBg"
        app:layout_constraintTop_toTopOf="@id/deviceFollowScreenBg"
        app:layout_constraintVertical_chainStyle="packed" />


    <include
        android:id="@+id/controlArrowStarry2Starry"
        layout="@layout/include_cec_control"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5px"
        app:layout_constraintBottom_toBottomOf="@id/cecDeviceScreenTv"
        app:layout_constraintLeft_toRightOf="@id/cecDeviceScreenTv" />


    <TextView
        style="@style/tv_cec_item_device"
        android:text="@string/cec_device_starry"
        app:layout_constraintBaseline_toBaselineOf="@id/cecDeviceScreenTv"
        app:layout_constraintLeft_toRightOf="@id/controlArrowStarry2Starry" />


    <TextView
        android:id="@+id/deviceFollowScreenHintTv"
        style="@style/tv_cec_item_hint"
        android:text="@string/cec_device_follow_screen"
        app:layout_constraintBottom_toBottomOf="@id/deviceFollowScreenBg"
        app:layout_constraintLeft_toLeftOf="@id/deviceFollowScreenBg"
        app:layout_constraintTop_toBottomOf="@id/cecDeviceScreenTv" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/deviceFollowScreenSwitch"
        style="@style/switch_cec_item"
        app:layout_constraintBottom_toBottomOf="@id/deviceFollowScreenBg"
        app:layout_constraintRight_toRightOf="@id/deviceFollowScreenBg"
        app:layout_constraintTop_toTopOf="@id/deviceFollowScreenBg" />

    <Space
        android:id="@+id/space"
        android:layout_width="wrap_content"
        android:layout_height="30px" />

    <com.czur.uilib.bg.CZItemBgView
        android:id="@+id/reminderBg"
        android:layout_width="900px"
        android:layout_height="190px"
        app:czIgnoreHover="true" />

    <TextView
        android:id="@+id/cecReminderTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:text="@string/cec_friendly_reminder"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/cecReminderContentTv1"
        app:layout_constraintLeft_toLeftOf="@id/reminderBg"
        app:layout_constraintTop_toTopOf="@id/reminderBg"
        app:layout_constraintVertical_chainStyle="packed" />


    <TextView
        android:id="@+id/cecReminderContentTv1"
        style="@style/tv_cec_friendly_reminder_content"
        android:layout_marginTop="12px"
        android:text="@string/cec_friendly_reminder_content1"
        app:layout_constraintBottom_toTopOf="@id/cecReminderContentTv2"
        app:layout_constraintLeft_toLeftOf="@id/cecReminderTitleTv"
        app:layout_constraintTop_toBottomOf="@id/cecReminderTitleTv"

        />

    <TextView
        android:id="@+id/cecReminderContentTv2"
        style="@style/tv_cec_friendly_reminder_content"
        android:layout_marginTop="8px"
        android:text="@string/cec_friendly_reminder_content2"
        app:layout_constraintBottom_toBottomOf="@id/reminderBg"
        app:layout_constraintLeft_toLeftOf="@id/cecReminderTitleTv"
        app:layout_constraintTop_toBottomOf="@id/cecReminderContentTv1" />
    
    <ImageView
        android:id="@+id/cecHelperIv"
        android:layout_width="32px"
        android:layout_height="32px"
        app:layout_constraintTop_toTopOf="@id/cecReminderContentTv2"
        app:layout_constraintBottom_toBottomOf="@id/cecReminderContentTv2"
        app:layout_constraintLeft_toRightOf="@id/cecReminderContentTv2"
        android:src="@drawable/ic_cec_question_mark"/>
</androidx.constraintlayout.widget.ConstraintLayout>