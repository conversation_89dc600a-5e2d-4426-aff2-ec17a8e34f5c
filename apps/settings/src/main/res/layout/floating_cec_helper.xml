<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="1324px"
    android:layout_height="900px"
    tools:ignore="PxUsage">

    <com.noober.background.view.BLView
        android:id="@+id/bgTopView"
        android:layout_width="match_parent"
        android:layout_height="86px"
        app:bl_corners_topLeftRadius="10px"
        app:bl_corners_topRightRadius="10px"
        app:bl_solid_color="#4f6de3"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#4f6de3" />

    <com.noober.background.view.BLView
        android:id="@+id/knowItBgView"
        android:layout_width="match_parent"
        android:layout_height="0px"
        app:bl_corners_bottomLeftRadius="10px"
        app:bl_corners_bottomRightRadius="10px"
        app:bl_solid_color="@color/bg_main_blue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bgTopView" />

    <ImageView
        android:id="@+id/closeBtn"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginRight="20px"
        android:src="@drawable/ic_dialog_close"
        app:float_tips="@string/float_tip_close"
        app:layout_constraintBottom_toBottomOf="@id/bgTopView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/bgTopView" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="@string/cec_helper_title"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/bgTopView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/bgTopView" />

    <TextView
        android:id="@+id/disclaimerTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8px"
        android:text="@string/cec_helper_disclaimer"
        android:textColor="#B3FFFFFF"
        android:textSize="18px"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <com.czur.uilib.btn.CZButton
        android:layout_width="220px"
        android:layout_height="50px"
        android:layout_marginBottom="8px"
        android:text="@string/dialog_normal_know_it"
        android:textSize="20px"
        app:colorStyle="SkyBlueWhite"
        app:layout_constraintBottom_toTopOf="@id/disclaimerTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/cecHelperRv"
        android:layout_width="1180px"
        android:scrollbars="vertical"
        android:fadeScrollbars="false"
        android:overScrollMode="always"
        android:layout_height="610px"
        android:layout_marginTop="60px"
        tools:listitem="@layout/item_cec_helper"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bgTopView" />


</androidx.constraintlayout.widget.ConstraintLayout>