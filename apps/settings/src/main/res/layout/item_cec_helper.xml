<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="40px"
    android:paddingBottom="20px"
    tools:background="#5879FC"
    tools:ignore="PxUsage">

    <ImageView
        android:id="@+id/tvIconIv"
        android:layout_width="32px"
        android:layout_height="32px"
        android:src="@drawable/ic_cec_tv"
        app:layout_constraintBottom_toBottomOf="@id/tvBrandTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvBrandTv" />

    <TextView
        android:id="@+id/tvBrandTv"
        android:layout_width="wrap_content"
        android:layout_height="46px"
        android:layout_marginLeft="15px"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="28px"
        android:textStyle="bold"
        app:layout_constraintLeft_toRightOf="@id/tvIconIv"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/cec_helper_content_brand_hisense" />

    <TextView
        android:id="@+id/tvContentTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#B3FFFFFF"
        android:textSize="22px"
        android:textStyle="normal"
        app:layout_constraintLeft_toLeftOf="@id/tvBrandTv"
        app:layout_constraintTop_toBottomOf="@id/tvBrandTv"
        tools:text="@string/cec_helper_content_hisense" />

</androidx.constraintlayout.widget.ConstraintLayout>