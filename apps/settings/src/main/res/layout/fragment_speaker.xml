<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/white"
    tools:ignore="PxUsage">

    <TextView
        android:id="@+id/titleTv"
        style="@style/speaker_mic_content_title_tv"
        android:layout_marginTop="72px"
        android:text="@string/sub_menu_title_speaker_choose"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.uilib.choose.CZCheckBox
        android:id="@+id/systemAutoChooseCb"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginTop="15px"
        android:layout_marginRight="25px"
        app:layout_constraintRight_toLeftOf="@id/systemAutoChooseTv"
        app:layout_constraintTop_toBottomOf="@id/titleTv" />

    <TextView
        android:id="@+id/systemAutoChooseTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/audio_device_choose_auto"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/systemAutoChooseCb"
        app:layout_constraintLeft_toLeftOf="@id/speakerRv"
        app:layout_constraintTop_toTopOf="@id/systemAutoChooseCb" />

    <com.czur.uilib.choose.CZCheckBox
        android:id="@+id/manualChooseCb"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginTop="32px"
        app:layout_constraintLeft_toLeftOf="@id/systemAutoChooseCb"
        app:layout_constraintTop_toBottomOf="@id/systemAutoChooseCb" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/audio_device_choose_manual"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/manualChooseCb"
        app:layout_constraintLeft_toLeftOf="@id/systemAutoChooseTv"
        app:layout_constraintTop_toTopOf="@id/manualChooseCb" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/speakerRv"
        android:layout_width="900px"
        android:layout_height="wrap_content"
        android:layout_marginTop="24px"
        app:layout_constraintHeight_max="320px"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/manualChooseCb"
        tools:layout_height="320px"
        tools:listitem="@layout/item_audio_device_info" />


    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/mediaBg"
        android:layout_width="882px"
        android:layout_height="132px"
        android:layout_marginBottom="72px"
        app:ignoreHover="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:position="single" />

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/callBg"
        android:layout_width="882px"
        android:layout_height="132px"
        android:layout_marginBottom="15px"
        app:ignoreHover="true"
        app:layout_constraintBottom_toTopOf="@id/mediaBg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/volumeTitleTv"
        style="@style/speaker_mic_content_title_tv"
        android:layout_marginTop="72px"
        android:layout_marginBottom="24px"
        android:text="@string/voice"
        app:layout_constraintBottom_toTopOf="@id/callBg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/titleCallTv"
        style="@style/TVVolumeTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="55px"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/volume_title_call"
        app:layout_constraintBottom_toBottomOf="@id/callBg"
        app:layout_constraintLeft_toLeftOf="@id/callBg"
        app:layout_constraintTop_toTopOf="@id/callBg" />

    <TextView
        android:id="@+id/titleMediaTv"
        style="@style/TVVolumeTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="55px"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="@string/volume_title_media"
        app:layout_constraintBottom_toBottomOf="@id/mediaBg"
        app:layout_constraintLeft_toLeftOf="@id/mediaBg"
        app:layout_constraintTop_toTopOf="@id/mediaBg" />

    <com.czur.uilib.seek.CZSeekBar
        android:id="@+id/callSeekBar"
        android:layout_width="456px"
        android:layout_height="wrap_content"
        android:layout_marginRight="25px"
        app:actionBtnSpace="false"
        app:layout_constraintBottom_toBottomOf="@id/callBg"
        app:layout_constraintRight_toRightOf="@id/callBg"
        app:layout_constraintTop_toTopOf="@id/callBg"
        app:minusBtnSrc="@drawable/ic_volume_minus"
        app:plusBtnSrc="@drawable/ic_volume_plus" />

    <com.czur.uilib.seek.CZSeekBar
        android:id="@+id/mediaSeekBar"
        android:layout_width="456px"
        android:layout_height="wrap_content"
        android:layout_marginRight="25px"
        app:actionBtnSpace="false"
        app:layout_constraintBottom_toBottomOf="@id/mediaBg"
        app:layout_constraintRight_toRightOf="@id/mediaBg"
        app:layout_constraintTop_toTopOf="@id/mediaBg"
        app:minusBtnSrc="@drawable/ic_volume_minus"
        app:plusBtnSrc="@drawable/ic_volume_plus" />

</androidx.constraintlayout.widget.ConstraintLayout>