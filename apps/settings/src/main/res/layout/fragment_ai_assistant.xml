<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/white"
    tools:ignore="PxUsage,RtlHardcoded">

    <View
        android:id="@+id/backgroundView"
        android:layout_width="match_parent"
        android:layout_height="488px"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/bgItem"
        android:layout_width="900px"
        android:layout_height="356px"
        app:ignoreHover="true"
        app:layout_constraintLeft_toLeftOf="@id/backgroundView"
        app:layout_constraintRight_toRightOf="@id/backgroundView"
        app:layout_constraintTop_toTopOf="@id/backgroundView"
        app:position="single" />

    <com.czur.uilib.bg.CZItemBgView
        android:id="@+id/bgItemTitle"
        android:layout_width="900px"
        android:layout_height="100px"
        app:czIgnoreHover="true"
        app:czItemPosition="top"
        app:czSelected="true"
        app:layout_constraintLeft_toLeftOf="@id/backgroundView"
        app:layout_constraintRight_toRightOf="@id/backgroundView"
        app:layout_constraintTop_toTopOf="@id/backgroundView" />

    <TextView
        android:id="@+id/titleItemTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:layout_marginTop="30px"
        android:text="@string/label_ai_assistant_voice_wakeup"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/bgItem"
        app:layout_constraintTop_toTopOf="@id/bgItem" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/totalSwitch"
        style="@style/switch_cec_item"
        app:czSwitchBgOffColor="#5879FC"
        app:czSwitchBgOnColor="@color/white"
        app:czSwitchBorderColor="@color/white"
        app:czSwitchTextOffColor="@color/white"
        app:czSwitchTextOnColor="#5879FC"
        app:czSwitchThumbOffColor="@color/white"
        app:czSwitchThumbOnColor="#5879FC"
        app:layout_constraintRight_toRightOf="@id/bgItem"
        app:layout_constraintTop_toTopOf="@id/titleItemTv" />

    <TextView
        android:id="@+id/hintItemTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="42px"
        android:text="@string/label_ai_assistant_voice_wakeup_hint"
        android:textColor="@color/text_common"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/titleItemTv"
        app:layout_constraintTop_toBottomOf="@id/bgItemTitle" />

    <TextView
        android:id="@+id/hintItemTv2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="-14px"
        android:text="@string/label_ai_assistant_voice_wakeup_hint_summary"
        android:textColor="@color/text_common"
        android:textSize="20px"
        android:textStyle="normal"
        app:layout_constraintLeft_toLeftOf="@id/hintItemTv"
        app:layout_constraintTop_toBottomOf="@id/hintItemTv" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/voiceWakeupSwitch"
        android:layout_width="78px"
        android:layout_height="40px"
        app:layout_constraintBottom_toBottomOf="@+id/hintItemTv2"
        app:layout_constraintRight_toRightOf="@id/totalSwitch"
        app:layout_constraintTop_toTopOf="@id/hintItemTv" />

    <TextView
        android:id="@+id/onlyTextTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="50px"
        android:text="@string/label_ai_assistant_only_text"
        android:textColor="@color/text_common"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/hintItemTv"
        app:layout_constraintTop_toBottomOf="@id/hintItemTv2" />

    <TextView
        android:id="@+id/onlyTextTv2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/label_ai_assistant_only_text_summary"
        android:textColor="@color/text_common"
        android:textSize="20px"
        android:textStyle="normal"
        app:layout_constraintLeft_toLeftOf="@id/hintItemTv2"
        app:layout_constraintTop_toBottomOf="@id/onlyTextTv" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/onlyTextSwitch"
        android:layout_width="78px"
        android:layout_height="40px"
        app:layout_constraintBottom_toBottomOf="@+id/onlyTextTv2"
        app:layout_constraintRight_toRightOf="@id/totalSwitch"
        app:layout_constraintTop_toTopOf="@id/onlyTextTv" />

    <com.czur.uilib.choose.CZCheckBox
        android:id="@+id/agreementCb"
        android:layout_width="30px"
        android:layout_height="30px"
        android:layout_marginBottom="10px"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/agreementTv"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/agreementTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5px"
        android:layout_marginBottom="10px"
        android:text="@string/str_hint_ai_assistant_agreement"
        android:textColor="@color/text_common"
        android:textSize="20px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/agreementCb" />

</androidx.constraintlayout.widget.ConstraintLayout>